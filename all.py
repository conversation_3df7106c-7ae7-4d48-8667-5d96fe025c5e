import pandas as pd
import numpy as np
from collections import defaultdict
from scipy.stats import entropy
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import matplotlib
import random
from itertools import product
import json
import os
import sys
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import time
from datetime import datetime, timedelta
matplotlib.use("TkAgg")

# 默认配置
DEFAULT_CONFIG = {
    'alpha': 2.0,
    'lambda': 0.1,
    'short_weight': 0.1,
    'long_weight': 0.65,
    'co_weight': 0.25,
    'hot_threshold': 1.5,
    'cold_threshold': 7.0
}

def resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

class ConfigManager:
    """配置管理器，为每个标签页提供独立的配置管理"""
    def __init__(self, version_number):
        self.version = version_number
        self.config_file = os.path.join(os.path.dirname(__file__), f'config{version_number}.json')
        self.init_file = f"init{version_number}.json"
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # 如果配置文件不存在，创建默认配置
            default_config = DEFAULT_CONFIG.copy()
            self.save_config(default_config)
            return default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return DEFAULT_CONFIG.copy()

    def save_config(self, config):
        """保存配置到文件"""
        try:
            print(f"尝试保存配置到文件: {self.config_file}")
            # 确保配置文件所在目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False

    def load_init_config(self):
        """加载初始化配置文件"""
        try:
            if os.path.exists(self.init_file):
                with open(self.init_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"加载初始化配置文件失败: {e}")
            return None

    def apply_init_config(self):
        """应用初始化配置"""
        init_config = self.load_init_config()
        if init_config is None:
            return False, "初始化配置文件不存在或格式错误"
        
        try:
            # 验证初始化配置中是否包含所有必要的参数
            required_params = ['alpha', 'lambda', 'short_weight', 'long_weight', 
                             'co_weight', 'hot_threshold', 'cold_threshold']
            if not all(param in init_config for param in required_params):
                return False, "初始化配置文件缺少必要的参数"
                
            # 验证权重之和是否为1
            weights_sum = init_config['short_weight'] + init_config['long_weight'] + init_config['co_weight']
            if abs(weights_sum - 1.0) > 0.001:
                return False, "权重参数之和必须等于1"
                
            # 保存到对应的config文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(init_config, f, indent=4, ensure_ascii=False)
            return True, "初始化参数已成功应用"
        except Exception as e:
            return False, f"应用初始化参数失败: {str(e)}"

class MFTNModel:
    def __init__(self, params=None, version=5):
        # 使用配置文件中的参数或用户提供的参数
        self.params = params or DEFAULT_CONFIG.copy()
        self.version = version  # 版本号，用于区分不同的行为
        self.history = None
        self.heat_index = None
        self.miss_counts = None
        self.position_triplets = None
        self.corr_matrix = None
        
    def fit(self, history_data):
        """训练模型"""
        self.history = np.array(history_data)
        self._initialize_thermal_state()
        self._build_correlation_matrix()
        self._find_position_triplets()
        
    def predict_next(self):
        """预测下一期号码"""
        if self.history is None:
            raise ValueError("模型未训练，请先调用fit方法")
            
        predictions = {}
        for pos in range(5):
            # 三层预测融合
            short_term = self._short_term_predict(pos)
            long_term = self._long_term_predict(pos)
            co_prediction = self._co_predict(pos)
            
            # 融合权重
            weights = self.params['short_weight'] * short_term + \
                     self.params['long_weight'] * long_term + \
                     self.params['co_weight'] * co_prediction
                     
            # 量子选择
            predictions[pos] = self._quantum_selection(weights, pos)
            
        return predictions
    
    # 内部实现方法
    def _initialize_thermal_state(self):
        """初始化热力状态"""
        n_periods, n_positions = self.history.shape
        self.heat_index = np.ones((n_positions, 10))
        self.miss_counts = np.zeros((n_positions, 10))
        
        # 计算初始遗漏状态
        for pos in range(n_positions):
            for num in range(10):
                mask = self.history[:, pos] == num
                if any(mask):
                    last_occur = np.where(mask)[0][-1]
                    self.miss_counts[pos, num] = n_periods - last_occur - 1
                else:
                    self.miss_counts[pos, num] = n_periods
    
    def _build_correlation_matrix(self):
        """构建位置相关系数矩阵"""
        n_positions = self.history.shape[1]
        self.corr_matrix = np.zeros((n_positions, n_positions))
        
        for i in range(n_positions):
            for j in range(n_positions):
                if i != j:
                    cov = np.cov(self.history[:, i], self.history[:, j])[0, 1]
                    var_i = np.var(self.history[:, i])
                    var_j = np.var(self.history[:, j])
                    self.corr_matrix[i, j] = cov / (np.sqrt(var_i) * np.sqrt(var_j))
    
    def _find_position_triplets(self):
        """为每个位置寻找最优三联位置"""
        self.position_triplets = {}
        n_positions = self.history.shape[1]
        
        for target_pos in range(n_positions):
            max_corr = 0
            best_triplet = None
            
            # 从其他位置中选择三个
            other_positions = [p for p in range(n_positions) if p != target_pos]
            for i in range(len(other_positions)):
                for j in range(i+1, len(other_positions)):
                    for k in range(j+1, len(other_positions)):
                        triplet = [other_positions[i], other_positions[j], other_positions[k]]
                        corr_product = np.prod([abs(self.corr_matrix[target_pos, p]) for p in triplet])
                        
                        if corr_product > max_corr:
                            max_corr = corr_product
                            best_triplet = triplet
            
            self.position_triplets[target_pos] = best_triplet
    
    def _short_term_predict(self, position):
        """短期动态预测"""
        window = 30
        if len(self.history) < window:
            return np.ones(10) / 10  # 均匀分布
        
        recent_data = self.history[-window:, position]
        last_num = self.history[-1, position]
        
        # 条件概率
        cond_prob = np.zeros(10)
        for d in range(10):
            count = np.sum((recent_data[:-1] == last_num) & (recent_data[1:] == d))
            total = np.sum(recent_data[:-1] == last_num)
            cond_prob[d] = (count + 10 * self.params['alpha']) / (total + self.params['alpha'])
        
        # 热度因子
        heat = np.array([np.sum(recent_data == d) / window for d in range(10)])
        
        # 冷号衰减
        cold = np.exp(-self.params['lambda'] * self.miss_counts[position])
        
        return 0.7 * cond_prob + 0.3 * (heat * cold)
    
    def _long_term_predict(self, position):
        """长期规律建模"""
        # 整体频率
        total_counts = np.bincount(self.history[:, position], minlength=10)
        total_probs = total_counts / len(self.history)
        
        # 熵权法
        entropies = -np.sum(total_probs * np.log2(total_probs + 1e-10))
        weights = (1 - entropies) / np.sum(1 - entropies)
        
        return weights * total_probs
    
    def _co_predict(self, position):
        """位置协同预测"""
        if position not in self.position_triplets or not self.position_triplets[position]:
            return np.ones(10) / 10
        
        triplet = self.position_triplets[position]
        joint_probs = np.zeros(10)
        
        # 简化的联合概率估计
        for d in range(10):
            mask = self.history[:, position] == d
            if np.any(mask):
                triplet_values = self.history[mask][:, triplet]
                unique, counts = np.unique(triplet_values, axis=0, return_counts=True)
                max_count = np.max(counts) if len(counts) > 0 else 0
                joint_probs[d] = max_count / len(mask)
        
        return joint_probs / np.sum(joint_probs)
    
    def _quantum_selection(self, weights, position):
        """量子化选择系统"""
        # 归一化权重
        weights = weights / np.sum(weights)

        # 冷热平衡
        for d in range(10):
            if self.heat_index[position, d] > self.params['hot_threshold']:
                weights[d] *= 0.8
            elif self.miss_counts[position, d] > self.params['cold_threshold']:
                weights[d] *= 1.2

        # 根据版本号确定选择数量
        if self.version == 5:
            total_nums = 5
        elif self.version == 6:
            total_nums = 6
        elif self.version == 7:
            total_nums = 7
        elif self.version == 8:
            total_nums = 8
        elif self.version == 9:
            total_nums = 9
        else:
            total_nums = 5

        # 新的选择策略：70%选最高权重，30%选最低权重
        import math
        high_count = math.ceil(total_nums * 0.7)  # 进位取整
        low_count = math.floor(total_nums * 0.3)  # 去尾取整

        # 确保总数正确
        if high_count + low_count != total_nums:
            high_count = total_nums - low_count

        # 获取权重排序的索引
        sorted_indices = np.argsort(weights)

        # 选择最高权重的数字
        high_weight_nums = sorted_indices[-high_count:][::-1]  # 最高权重，降序排列

        # 选择最低权重的数字
        low_weight_nums = sorted_indices[:low_count]  # 最低权重，升序排列

        # 合并结果，先高权重后低权重
        selected_nums = np.concatenate([high_weight_nums, low_weight_nums])

        return selected_nums.tolist()

class ParameterOptimizer:
    def __init__(self, target_hit_rate=0.8, max_iterations=1000, population_size=50, num_threads=4, period=None):
        self.target_hit_rate = target_hit_rate
        self.max_iterations = max_iterations
        self.population_size = population_size
        self.num_threads = num_threads
        self.progress_callback = None
        self.optimization_running = True
        self.result_lock = threading.Lock()
        self.thread_results = []
        self.best_result = {'hit_rate': 0.0, 'params': None}
        self.best_hit_rate = 0.0
        self.current_iteration = 0
        self.iteration_lock = threading.Lock()
        self.stop_event = threading.Event()
        self.param_ranges = {
            'alpha': (0.1, 10.0),
            'lambda': (0.01, 1.0),
            'short_weight': (0.05, 0.5),
            'long_weight': (0.2, 0.8),
            'co_weight': (0.05, 0.5),
            'hot_threshold': (1.0, 5.0),
            'cold_threshold': (3.0, 20.0)
        }
        self.optimization_history = []
        self.best_params = None
        self.elite_size = 5
        self.crossover_rate = 0.8
        self.mutation_rate = 0.2
        self.period = period  # 新增

    def _log(self, msg):
        prefix = f"【{self.period}天周期】" if self.period else ""
        print(f"{prefix}{msg}")

    def optimize(self, evaluate_function):
        self._log("开始优化过程...")
        self.optimization_running = True
        self.current_iteration = 0
        self.thread_results = []
        self.best_hit_rate = 0.0
        self.best_params = None
        population = []
        self._log("生成初始种群...")
        while len(population) < self.population_size:
            params = self._generate_random_params()
            total_weight = params['short_weight'] + params['long_weight'] + params['co_weight']
            if abs(total_weight - 1.0) < 0.1:
                population.append(params)
                hit_rate = evaluate_function(params)
                if hit_rate > self.best_hit_rate:
                    self.best_hit_rate = hit_rate
                    self.best_params = params.copy()
                    self._log(f"初始种群发现更好结果：{hit_rate*100:.2f}%")
        self._log(f"初始种群生成完成，共 {len(population)} 个个体")
        for iteration in range(1, self.max_iterations + 1):
            if not self.optimization_running:
                self._log("优化被停止")
                break
            self._log(f"开始第 {iteration} 次迭代")
            self.current_iteration = iteration
            evaluated_population = []
            for i, params in enumerate(population):
                if not self.optimization_running:
                    break
                try:
                    hit_rate = evaluate_function(params)
                    evaluated_population.append((params, hit_rate))
                    if hit_rate > self.best_hit_rate:
                        self.best_hit_rate = hit_rate
                        self.best_params = params.copy()
                        self.optimization_history.append({
                            'iteration': iteration,
                            'params': params.copy(),
                            'hit_rate': hit_rate
                        })
                        self._log(f"第 {iteration} 次迭代发现更好结果：{hit_rate*100:.2f}%")
                except Exception as e:
                    self._log(f"评估参数时发生错误: {e}")
                    continue
            if not evaluated_population:
                self._log("没有成功评估的个体，跳过此次迭代")
                continue
            evaluated_population.sort(key=lambda x: x[1], reverse=True)
            current_best_rate = evaluated_population[0][1]
            current_best_params = evaluated_population[0][0]
            self._log(f"第 {iteration} 次迭代完成，当前最佳命中率：{current_best_rate*100:.2f}%")
            if self.progress_callback:
                try:
                    if not self.progress_callback(iteration, self.max_iterations, current_best_rate, current_best_params):
                        self._log("进度回调返回False，停止优化")
                        break
                except Exception as e:
                    self._log(f"进度回调发生错误: {e}")
            new_population = [p[0] for p in evaluated_population[:self.elite_size]]
            generation_attempts = 0
            while len(new_population) < self.population_size and generation_attempts < 100:
                try:
                    parent1 = self._tournament_selection(evaluated_population)
                    parent2 = self._tournament_selection(evaluated_population)
                    child = self._crossover(parent1, parent2)
                    child = self._mutate(child)
                    total_weight = child['short_weight'] + child['long_weight'] + child['co_weight']
                    if abs(total_weight - 1.0) < 0.1:
                        new_population.append(child)
                except Exception as e:
                    self._log(f"生成子代时发生错误: {e}")
                generation_attempts += 1
            while len(new_population) < self.population_size:
                try:
                    random_params = self._generate_random_params()
                    new_population.append(random_params)
                except Exception as e:
                    self._log(f"生成随机个体时发生错误: {e}")
                    break
            population = new_population
            self._log(f"第 {iteration} 次迭代生成了 {len(population)} 个新个体")
        self._log(f"优化完成，最佳命中率：{self.best_hit_rate*100:.2f}%")
        return self.best_params, self.best_hit_rate

    def stop_optimization(self):
        """停止优化过程"""
        self.optimization_running = False
        self.stop_event.set()  # 设置停止事件

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _generate_random_params(self):
        """生成随机参数组合"""
        params = {}
        for param, (min_val, max_val) in self.param_ranges.items():
            if param in ['short_weight', 'long_weight', 'co_weight']:
                continue
            # 使用更细致的随机生成
            if param in ['alpha', 'lambda']:
                # 对于这些参数使用对数分布
                log_min = np.log(min_val)
                log_max = np.log(max_val)
                value = np.exp(random.uniform(log_min, log_max))
            else:
                value = random.uniform(min_val, max_val)
            params[param] = value
        
        # 使用Dirichlet分布生成权重，确保和为1
        while True:
            # 使用不同的alpha值来控制分布
            weights = np.random.dirichlet(np.array([2, 4, 2]))  # 偏向让long_weight较大
            short_w, long_w, co_w = weights
            
            # 检查是否在允许范围内
            if (self.param_ranges['short_weight'][0] <= short_w <= self.param_ranges['short_weight'][1] and
                self.param_ranges['long_weight'][0] <= long_w <= self.param_ranges['long_weight'][1] and
                self.param_ranges['co_weight'][0] <= co_w <= self.param_ranges['co_weight'][1]):
                params['short_weight'] = short_w
                params['long_weight'] = long_w
                params['co_weight'] = co_w
                break
        
        return params
    
    def _crossover(self, parent1, parent2):
        """参数交叉"""
        if random.random() > self.crossover_rate:
            return parent1.copy()
            
        child = {}
        # 对非权重参数进行均匀交叉
        for param in parent1:
            if param not in ['short_weight', 'long_weight', 'co_weight']:
                if random.random() < 0.5:
                    child[param] = parent1[param]
                else:
                    child[param] = parent2[param]
        
        # 对权重参数进行特殊处理
        weights1 = np.array([parent1['short_weight'], parent1['long_weight'], parent1['co_weight']])
        weights2 = np.array([parent2['short_weight'], parent2['long_weight'], parent2['co_weight']])
        
        # 使用算术交叉并确保和为1
        alpha = random.random()
        new_weights = alpha * weights1 + (1 - alpha) * weights2
        
        # 归一化确保和为1
        new_weights = new_weights / np.sum(new_weights)
        
        # 检查是否在允许范围内，如果不在则重新生成
        if not (self.param_ranges['short_weight'][0] <= new_weights[0] <= self.param_ranges['short_weight'][1] and
                self.param_ranges['long_weight'][0] <= new_weights[1] <= self.param_ranges['long_weight'][1] and
                self.param_ranges['co_weight'][0] <= new_weights[2] <= self.param_ranges['co_weight'][1]):
            # 如果超出范围，使用Dirichlet分布重新生成
            new_weights = np.random.dirichlet(np.array([2, 4, 2]))
        
        child['short_weight'] = new_weights[0]
        child['long_weight'] = new_weights[1]
        child['co_weight'] = new_weights[2]
        
        return child
    
    def _mutate(self, params):
        """参数变异"""
        mutated = params.copy()
        
        # 对每个非权重参数进行变异
        for param in params:
            if param not in ['short_weight', 'long_weight', 'co_weight']:
                if random.random() < self.mutation_rate:
                    min_val, max_val = self.param_ranges[param]
                    current_val = params[param]
                    
                    # 使用正态分布进行变异
                    sigma = (max_val - min_val) * 0.1  # 标准差为范围的10%
                    new_val = random.gauss(current_val, sigma)
                    
                    # 确保在范围内
                    new_val = max(min_val, min(max_val, new_val))
                    mutated[param] = new_val
        
        # 权重参数的变异
        if random.random() < self.mutation_rate:
            weights = np.array([mutated['short_weight'], mutated['long_weight'], mutated['co_weight']])
            
            # 使用Dirichlet分布进行变异
            # 使用当前权重作为基准，添加扰动
            alpha = weights * 10  # 控制变异程度
            new_weights = np.random.dirichlet(alpha)
            
            # 检查是否在允许范围内
            if (self.param_ranges['short_weight'][0] <= new_weights[0] <= self.param_ranges['short_weight'][1] and
                self.param_ranges['long_weight'][0] <= new_weights[1] <= self.param_ranges['long_weight'][1] and
                self.param_ranges['co_weight'][0] <= new_weights[2] <= self.param_ranges['co_weight'][1]):
                mutated['short_weight'] = new_weights[0]
                mutated['long_weight'] = new_weights[1]
                mutated['co_weight'] = new_weights[2]
        
        return mutated
    
    def _tournament_selection(self, evaluated_population):
        """锦标赛选择法"""
        tournament_size = max(3, self.population_size // 10)
        participants = random.sample(evaluated_population, tournament_size)
        return max(participants, key=lambda x: x[1])[0]
    
    def get_optimization_summary(self):
        """获取优化过程的摘要信息"""
        if not self.optimization_history:
            return "尚未进行优化"
        
        summary = []
        summary.append("优化过程摘要:")
        summary.append("-" * 50)
        
        # 按命中率排序
        sorted_history = sorted(self.optimization_history, key=lambda x: x['hit_rate'], reverse=True)
        
        # 输出前5个最佳结果
        for i, record in enumerate(sorted_history[:5], 1):
            summary.append(f"\n第{i}优结果 (迭代{record['iteration']}):")
            summary.append(f"命中率: {record['hit_rate']*100:.2f}%")
            summary.append("参数:")
            for param, value in record['params'].items():
                summary.append(f"  {param}: {value:.3f}")
            summary.append("-" * 30)
        
        return "\n".join(summary)

class LotteryPredictionApp:
    def __init__(self, root):
        self.root = root
        self.root.title("排列5智能杀码系统")
        self.root.geometry("1400x800")
        self.root.configure(bg="#f0f0f0")
        
        # 设置ttk样式
        self.style = ttk.Style()
        self.style.theme_use('default')
        
        # 配置主标签页样式
        self.style.configure(
            "Main.TNotebook", 
            background="#f0f0f0",
            borderwidth=0
        )
        self.style.configure(
            "Main.TNotebook.Tab",
            padding=[10, 5],
            background="#e0e0e0",
            foreground="#333333",
            font=("SimHei", 10)
        )
        self.style.map(
            "Main.TNotebook.Tab",
            background=[("selected", "#4CAF50"), ("active", "#81C784")],
            foreground=[("selected", "#ffffff"), ("active", "#000000")]
        )
        
        # 配置子标签页样式
        self.style.configure(
            "Sub.TNotebook", 
            background="#ffffff",
            borderwidth=0
        )
        self.style.configure(
            "Sub.TNotebook.Tab",
            padding=[8, 4],
            background="#f5f5f5",
            foreground="#666666",
            font=("SimHei", 9)
        )
        self.style.map(
            "Sub.TNotebook.Tab",
            background=[("selected", "#2196F3"), ("active", "#90CAF9")],
            foreground=[("selected", "#ffffff"), ("active", "#000000")]
        )
        
        # 设置工作空间路径和数据文件路径
        # 获取当前文件的绝对路径并规范化
        current_file = os.path.abspath(__file__)
        # 获取o1目录的路径（当前文件的上两级目录）
        self.workspace_root = os.path.dirname(os.path.dirname(current_file))
        # 设置数据文件的完整路径，使用正确的路径分隔符
        self.data_file = "F:\\o1\\data.xlsx"  # 直接使用固定路径
        
        # 设置应用程序图标
        try:
            icon_path = resource_path("app.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass
        
        # 版本信息
        self.versions = {
            8: "8位大奖"
        }
        
        # 只初始化8位大奖
        self.config_managers = {8: ConfigManager(8)}
        self.configs = {8: self.config_managers[8].load_config()}
        self.default_params = {8: {k: v for k, v in self.configs[8].items() if k != 'last_excel_file'}}
        self.models = {8: MFTNModel(self.default_params[8], 8)}
        
        # 共享数据
        self.history_data = None
        self.last_period = None
        self.periods_data = None
        
        # 只为8位大奖存储预测结果
        self.predictions = {8: None}
        
        # 线程管理
        self.running_threads = []
        
        # 只为8位大奖添加自动回测状态
        self.auto_backtest_running = {8: False}
        
        # 添加初始化完成标志，防止程序启动时触发自动回测
        self.initialization_complete = False
        
        # 优化周期管理
        self.optimization_cycle_days = 10  # 根据量化对比结果，最优周期为10天
        self.last_optimization_date = None
        self.optimization_reminder_shown = False
        # 新增：统一周期列表
        self.quantitative_periods = [5, 10, 15, 20, 25, 30]
        self.quantitative_results_file = os.path.join(os.path.dirname(__file__), 'quantitative_results.json')
        self.quantitative_results = {}
        self.load_quantitative_results()
        if not self.quantitative_results:
            self.quantitative_results = {
                5:  {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0},
                10: {'avg_hit_rate': 0.8795, 'full_hit_rate': 0.6682},
                15: {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0},
                20: {'avg_hit_rate': 0.8591, 'full_hit_rate': 0.5955},
                25: {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0},
                30: {'avg_hit_rate': 0.8423, 'full_hit_rate': 0.5548}
            }
        self.quantitative_analysis_parent_frame = None  # 新增
        self.create_widgets()                # 2. 创建主界面和所有控件
        self._refresh_quantitative_display() # 3. 刷新右侧量化对比区域
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 延迟自动加载数据文件和设置初始化完成标志
        self.root.after(1000, self.complete_initialization)
    
    def on_closing(self):
        """关闭程序时清理线程"""
        try:
            for thread in self.running_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            self.root.destroy()
        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()
    
    def browse_file(self):
        """浏览文件"""
        initial_dir = "F:\\o1"  # 直接使用固定的初始目录     
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")],
            initialdir=initial_dir,
            initialfile="data.xlsx"
        )
        if file_path:
            # 将路径转换为Windows格式
            file_path = file_path.replace('/', '\\')
            self.file_path_var.set(file_path)
            self.data_file = file_path
            # 保存文件路径到所有配置
            self.configs[8]['last_excel_file'] = file_path
            self.config_managers[8].save_config(self.configs[8])
    
    def delayed_auto_load(self):
        """延迟自动加载数据文件"""
        try:
            if os.path.exists(self.data_file):
                self.file_path_var.set(self.data_file)
                self.status_var.set("发现数据文件，准备自动加载...")
                self.root.update()
                self.root.after(500, self.safe_auto_load)
            else:
                # 即使文件不存在也显示默认路径
                self.file_path_var.set(self.data_file)
                self.status_var.set("未找到数据文件，请确认文件存在")
        except Exception as e:
            print(f"延迟加载检查时发生错误: {e}")
            self.status_var.set("就绪")
    
    def safe_auto_load(self):
        """安全地自动加载数据文件"""
        try:
            current_file = self.file_path_var.get()
            if current_file and current_file != "未选择文件" and os.path.exists(current_file):
                self.status_var.set("正在自动加载数据...")
                self.root.update()
                threading.Thread(target=self._safe_auto_backtest).start()
            else:
                self.status_var.set("请选择有效的数据文件")
        except Exception as e:
            print(f"安全自动加载时发生错误: {e}")
            self.status_var.set("就绪")
    
    def _safe_auto_backtest(self):
        """安全的自动回测线程"""
        try:
            # 直接使用8位大奖
            version = 8
            backtest_periods = int(self.backtest_periods_vars[8].get())
            self._run_backtest_and_predict(backtest_periods, version)
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set("自动加载失败，请手动操作"))
            print(f"自动回测时发生错误: {e}")
    
    def create_widgets(self):
        """创建主界面"""
        # 顶部控制框架
        top_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=10)
        top_frame.pack(fill=tk.X)
        
        # 文件选择
        file_label = tk.Label(top_frame, text="选择Excel文件:", bg="#f0f0f0", font=("SimHei", 10))
        file_label.pack(side=tk.LEFT, padx=5)
        
        # 设置默认文件路径（使用Windows格式的路径）
        self.file_path_var = tk.StringVar(value="F:\\o1\\data.xlsx")
        file_entry = tk.Entry(top_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, padx=5)
        
        browse_btn = tk.Button(top_frame, text="浏览", command=self.browse_file, font=("SimHei", 10),
                              bg="#4CAF50", fg="white", relief=tk.RAISED, padx=10)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加录入和修改开奖号码按钮
        add_result_btn = tk.Button(top_frame, text="录入开奖号码", 
                                 command=self.show_add_result_dialog,
                                 font=("SimHei", 10), bg="#673AB7", fg="white", relief=tk.RAISED, padx=10)
        add_result_btn.pack(side=tk.LEFT, padx=5)
        
        edit_result_btn = tk.Button(top_frame, text="修改开奖号码", 
                                  command=self.show_edit_result_dialog,
                                  font=("SimHei", 10), bg="#009688", fg="white", relief=tk.RAISED, padx=10)
        edit_result_btn.pack(side=tk.LEFT, padx=5)
        
        # 只保留预测结果和回测部分，无历史统计
        main_frame = tk.Frame(self.root, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 版本控制按钮
        self._create_version_controls(main_frame, 8)
        
        # 创建左右分栏
        content_frame = tk.Frame(main_frame, bg="#f0f0f0")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧：预测结果和回测
        left_frame = tk.Frame(content_frame, bg="#f0f0f0")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 预测结果
        self.result_frames = {8: tk.Frame(left_frame, bg="white")}
        self.result_frames[8].pack(fill=tk.X, padx=5, pady=5)
        
        # 回测
        self.backtest_frames = {8: tk.Frame(left_frame, bg="white")}
        self.backtest_frames[8].pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.backtest_periods_vars = {8: tk.StringVar(value="30")}
        self.backtest_status_vars = {8: tk.StringVar(value="请先选择文件并开始回测")}
        self.backtest_canvases = {8: None}
        self.backtest_scrollable_frames = {8: None}
        self._create_backtest_controls(self.backtest_frames[8], 8)
        
        # 右侧：量化对比分析
        right_frame = tk.Frame(content_frame, bg="#f0f0f0")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(5, 0))
        self.quantitative_analysis_parent_frame = right_frame  # 新增
        self._create_quantitative_analysis_frame(right_frame)
        
        # 优化提醒框架
        self.optimization_reminder_frame = tk.Frame(self.root, bg="#FFF3CD", bd=1, relief=tk.SUNKEN)
        self.optimization_reminder_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 优化提醒标签
        self.optimization_reminder_var = tk.StringVar()
        self.optimization_reminder_var.set("")
        self.optimization_reminder_label = tk.Label(self.optimization_reminder_frame, 
                                                   textvariable=self.optimization_reminder_var,
                                                   bg="#FFF3CD", fg="#856404", font=("SimHei", 9),
                                                   anchor=tk.W, padx=10, pady=5)
        self.optimization_reminder_label.pack(fill=tk.X)
        
        # 底部状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def on_tab_changed(self, event):
        # 只保留一个tab，不需要切换逻辑
        pass
    
    def auto_run_backtest_and_predict(self, version):
        # 只保留8位大奖
        if self.auto_backtest_running[8]:
            print("8位大奖正在执行回测，跳过重复执行")
            return
        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            return
        self.auto_backtest_running[8] = True
        backtest_periods = int(self.backtest_periods_vars[8].get())
        if backtest_periods <= 0:
            backtest_periods = 30
            self.backtest_periods_vars[8].set("30")
        self.status_var.set(f"正在为8位大奖自动加载数据...")
        self.root.update()
        threading.Thread(target=self._auto_run_backtest_and_predict_thread, args=(backtest_periods, 8), daemon=True).start()
    
    def _auto_run_backtest_and_predict_thread(self, backtest_periods, version):
        # 只保留8位大奖
        try:
            self._run_backtest_and_predict(backtest_periods, 8)
        finally:
            self.auto_backtest_running[8] = False
    
    def _create_version_controls(self, parent_frame, version):
        """为每个版本创建控制按钮"""
        control_frame = tk.Frame(parent_frame, bg="#f0f0f0", padx=10, pady=5)
        control_frame.pack(fill=tk.X)
        
        predict_btn = tk.Button(control_frame, text="回测并预测", 
                               command=lambda v=version: self.run_backtest_and_predict(v),
                               font=("SimHei", 10), bg="#2196F3", fg="white", relief=tk.RAISED, padx=10)
        predict_btn.pack(side=tk.LEFT, padx=5)
        
        params_btn = tk.Button(control_frame, text="参数设置", 
                              command=lambda v=version: self.show_params_dialog(v),
                              font=("SimHei", 10), bg="#FF9800", fg="white", relief=tk.RAISED, padx=10)
        params_btn.pack(side=tk.LEFT, padx=5)
        
        optimize_btn = tk.Button(control_frame, text="自动优化参数", 
                               command=lambda v=version: self.auto_optimize_params(v),
                               font=("SimHei", 10), bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        optimize_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加优化周期设置按钮
        cycle_btn = tk.Button(control_frame, text="优化周期设置", 
                            command=lambda v=version: self.show_optimization_cycle_dialog(v),
                            font=("SimHei", 10), bg="#795548", fg="white", relief=tk.RAISED, padx=10)
        cycle_btn.pack(side=tk.LEFT, padx=5)
        
        init_btn = tk.Button(control_frame, text="初始化参数", 
                           command=lambda v=version: self.init_params(v),
                           font=("SimHei", 10), bg="#673AB7", fg="white", relief=tk.RAISED, padx=10)
        init_btn.pack(side=tk.LEFT, padx=5)
    
    def _create_backtest_controls(self, backtest_frame, version):
        """为每个版本创建回测控制"""
        # 回测设置
        backtest_settings_frame = tk.Frame(backtest_frame, bg="white", padx=20, pady=10)
        backtest_settings_frame.pack(fill=tk.X)
        
        backtest_label = tk.Label(backtest_settings_frame, text="回测期数:", bg="white", font=("SimHei", 10))
        backtest_label.pack(side=tk.LEFT, padx=5)
        
        self.backtest_periods_vars[version] = tk.StringVar(value="30")
        backtest_entry = tk.Entry(backtest_settings_frame, textvariable=self.backtest_periods_vars[version], width=10)
        backtest_entry.pack(side=tk.LEFT, padx=5)
        
        # 新增回撤天数输入框
        rollback_label = tk.Label(backtest_settings_frame, text="回撤天数:", bg="white", font=("SimHei", 10))
        rollback_label.pack(side=tk.LEFT, padx=5)
        if not hasattr(self, 'backtest_rollback_vars'):
            self.backtest_rollback_vars = {}
        self.backtest_rollback_vars[version] = tk.StringVar(value="0")
        rollback_entry = tk.Entry(backtest_settings_frame, textvariable=self.backtest_rollback_vars[version], width=10)
        rollback_entry.pack(side=tk.LEFT, padx=5)
        
        backtest_btn = tk.Button(backtest_settings_frame, text="单独回测", 
                                command=lambda v=version: self.run_backtest(v),
                                font=("SimHei", 10), bg="#FF5722", fg="white", relief=tk.RAISED, padx=10)
        backtest_btn.pack(side=tk.LEFT, padx=5)
        
        self.backtest_status_vars[version] = tk.StringVar()
        self.backtest_status_vars[version].set("请先选择文件并开始回测")
        backtest_status_label = tk.Label(backtest_settings_frame, textvariable=self.backtest_status_vars[version], 
                                        bg="white", font=("SimHei", 10))
        backtest_status_label.pack(side=tk.LEFT, padx=10)
        
        # 回测结果框架 - 添加滚动条支持
        backtest_result_frame = tk.Frame(backtest_frame, bg="white")
        backtest_result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(backtest_result_frame, bg="white", highlightthickness=0)
        scrollbar = tk.Scrollbar(backtest_result_frame, orient="vertical", command=canvas.yview)
        xscrollbar = tk.Scrollbar(backtest_result_frame, orient="horizontal", command=canvas.xview)  # 横向滚动条
        scrollable_frame = tk.Frame(canvas, bg="white")
        
        # 配置Canvas滚动
        def on_configure(event, c=canvas, s=scrollable_frame):
            c.configure(scrollregion=c.bbox("all"))
            # 自动调整canvas宽度以适应内容
            if s.winfo_reqwidth() > c.winfo_width():
                c.configure(width=s.winfo_reqwidth())
        scrollable_frame.bind(
            "<Configure>",
            on_configure
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set, xscrollcommand=xscrollbar.set)
        
        # 放置Canvas和Scrollbar
        scrollbar.pack(side="right", fill="y")
        xscrollbar.pack(side="bottom", fill="x")
        canvas.pack(side="left", fill="both", expand=True)
        
        self.backtest_canvases[version] = canvas
        self.backtest_scrollable_frames[version] = scrollable_frame 

    def load_data(self, file_path):
        """加载数据"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"找不到文件: {file_path}")
                
            df = pd.read_excel(file_path)
            
            required_columns = ['期号', '千位', '百位', '十位', '个位', '球五']
            if not all(col in df.columns for col in required_columns):
                raise ValueError("Excel文件缺少必要的列")
            
            last_period = df['期号'].iloc[-1]
            periods_data = df['期号'].values
            data = df[['千位', '百位', '十位', '个位', '球五']].values
            
            if not np.all((data >= 0) & (data <= 9)):
                raise ValueError("开奖数据包含无效数字（应在0-9范围内）")
                
            return data, len(data), last_period, periods_data
        
        except Exception as e:
            messagebox.showerror("数据加载错误", str(e))
            return None, 0, None, None
    
    def run_backtest_and_predict(self, version):
        """执行回测并预测"""
        file_path = self.file_path_var.get()
        if file_path == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
        
        try:
            backtest_periods = int(self.backtest_periods_vars[version].get())
            if backtest_periods <= 0:
                messagebox.showwarning("警告", "回测期数必须大于0")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数")
            return
            
        self.status_var.set("正在加载数据...")
        self.root.update()
        
        threading.Thread(target=self._run_backtest_and_predict, args=(backtest_periods, version)).start()
    
    def _run_backtest_and_predict(self, backtest_periods, version):
        """执行回测并预测的后台线程"""
        try:
            # 加载数据
            data, num_periods, last_period, periods_data = self.load_data(self.file_path_var.get())
            if data is None:
                self.status_var.set("就绪")
                return
                
            self.history_data = data
            self.last_period = last_period
            self.periods_data = periods_data
            
            # 检查回测期数是否合理
            if backtest_periods >= len(data):
                backtest_periods = len(data) - 1
                self.backtest_periods_vars[version].set(str(backtest_periods))
                messagebox.showinfo("提示", f"回测期数已自动调整为: {backtest_periods}")
            
            # 执行回测
            self.status_var.set("正在进行回测...")
            self.backtest_status_vars[version].set("正在进行回测...")
            
            # 执行回测逻辑（简化版本，根据版本8的特殊性处理）
            total_periods = len(self.history_data)
            hit_rates = []
            full_hit_count = 0
            
            # 位置对的组合
            position_pairs = [
                ('千位', '百位', 0, 1), ('千位', '十位', 0, 2), ('千位', '个位', 0, 3),
                ('百位', '十位', 1, 2), ('百位', '个位', 1, 3),
                ('十位', '个位', 2, 3)
            ]
            
            # 初始化两两组合统计（只对非版本8进行）
            pair_stats = {}
            if version != 8:
                for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                    pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                    pair_stats[pair_key] = {'hit_count': 0, 'total_count': 0}
            
            backtest_results = []
            
            # 使用当前参数初始化模型
            current_model = MFTNModel(self.get_current_params(version), version)
            
            # 对每一期进行回测
            for i in range(total_periods - backtest_periods, total_periods):
                current_period = self.periods_data[i]
                actual_numbers = self.history_data[i]
                train_data = self.history_data[:i]
                
                current_model.fit(train_data)
                predictions = current_model.predict_next()
                
                # 计算命中情况
                position_hits = []
                for pos in range(4):  # 只比较前4位
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                
                # 计算两两组合的命中情况（只对非版本8进行）
                pair_hits = {}
                if version != 8:
                    for pair_name1, pair_name2, pos1, pos2 in position_pairs:
                        if pos1 < 4 and pos2 < 4:
                            pair_key = f"{pair_name1[0]}{pair_name2[0]}"
                            hit1 = actual_numbers[pos1] in predictions[pos1]
                            hit2 = actual_numbers[pos2] in predictions[pos2]
                            pair_hit = hit1 and hit2
                            pair_hits[pair_key] = pair_hit
                            
                            pair_stats[pair_key]['total_count'] += 1
                            if pair_hit:
                                pair_stats[pair_key]['hit_count'] += 1
                
                # 计算当前期的命中率
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                
                # 检查是否4位全中
                if all(position_hits):
                    full_hit_count += 1
                
                # 保存回测结果
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': pair_hits,
                    'hit_rate': hit_rate
                })
                
                # 更新状态
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_vars[version].set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
            
            # 计算总体命中率和4位全中率
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            
            # 更新UI显示回测结果
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, pair_stats, version)
            
            # 使用全部数据训练模型并进行预测
            self.status_var.set("正在训练模型...")
            self.root.update()
            
            # 使用当前参数初始化模型
            self.models[version] = MFTNModel(self.get_current_params(version), version)
            
            # 训练模型
            self.models[version].fit(self.history_data)
            
            self.status_var.set("正在预测...")
            self.root.update()
            
            # 预测
            self.predictions[version] = self.models[version].predict_next()
            
            # 更新UI显示预测结果
            self.root.after(0, self._update_result_display, num_periods, version)
            
            # 显示回测结果摘要
            if version != 8:
                pair_summary = "\n".join([f"{k}: {v['hit_count']}/{v['total_count']} ({v['hit_count']/v['total_count']*100:.1f}%)" 
                                        for k, v in pair_stats.items()])
                #self.root.after(0, messagebox.showinfo, "回测结果", 
                #               f"版本{version}回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%\n\n两两组合命中率:\n{pair_summary}")
            #else:
                #self.root.after(0, messagebox.showinfo, "回测结果", 
                #               f"版本{version}回测完成!\n回测期数: {len(backtest_results)}\n平均命中率: {overall_hit_rate*100:.2f}%\n4位全中率: {full_hit_rate*100:.2f}%")
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测或预测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.status_var.set, "就绪")
            self.root.after(0, self.backtest_status_vars[version].set, "回测完成")
    
    def run_backtest(self, version):
        file_path = self.file_path_var.get()
        if file_path == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return
        try:
            backtest_periods = int(self.backtest_periods_vars[version].get())
            rollback_days = int(self.backtest_rollback_vars[version].get()) if hasattr(self, 'backtest_rollback_vars') and version in self.backtest_rollback_vars else 0
            if backtest_periods <= 0:
                messagebox.showwarning("警告", "回测期数必须大于0")
                return
            if rollback_days < 0:
                messagebox.showwarning("警告", "回撤天数不能为负数")
                return
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的回测期数和回撤天数")
            return
        self.backtest_status_vars[version].set("正在进行回测...")
        threading.Thread(target=self._run_backtest, args=(backtest_periods, version, rollback_days)).start()

    def _run_backtest(self, backtest_periods, version, rollback_days=0):
        try:
            data, num_periods, last_period, periods_data = self.load_data(self.file_path_var.get())
            if data is None:
                self.backtest_status_vars[version].set("数据加载失败")
                return
            # 根据回撤天数裁剪数据
            if rollback_days > 0:
                data = data[:-rollback_days] if rollback_days < len(data) else np.array([])
                periods_data = periods_data[:-rollback_days] if rollback_days < len(periods_data) else np.array([])
            if len(data) == 0 or len(periods_data) == 0:
                self.backtest_status_vars[version].set("回撤天数过大，数据为空")
                return
            self.history_data = data
            self.last_period = last_period
            self.periods_data = periods_data
            if backtest_periods >= len(data):
                backtest_periods = len(data) - 1
                self.backtest_periods_vars[version].set(str(backtest_periods))
                messagebox.showinfo("提示", f"回测期数已自动调整为: {backtest_periods}")
            total_periods = len(self.history_data)
            hit_rates = []
            full_hit_count = 0
            backtest_results = []
            current_model = MFTNModel(self.get_current_params(version), version)
            for i in range(total_periods - backtest_periods, total_periods):
                current_period = self.periods_data[i]
                actual_numbers = self.history_data[i]
                train_data = self.history_data[:i]
                current_model.fit(train_data)
                predictions = current_model.predict_next()
                position_hits = []
                for pos in range(4):
                    predicted_nums = predictions[pos]
                    actual_num = actual_numbers[pos]
                    hit = actual_num in predicted_nums
                    position_hits.append(hit)
                hit_rate = sum(position_hits) / 4.0
                hit_rates.append(hit_rate)
                if all(position_hits):
                    full_hit_count += 1
                backtest_results.append({
                    'period': current_period,
                    'actual': actual_numbers,
                    'predictions': predictions,
                    'position_hits': position_hits,
                    'pair_hits': {},
                    'hit_rate': hit_rate
                })
                progress = (i - (total_periods - backtest_periods) + 1) / backtest_periods * 100
                self.root.after(0, self.backtest_status_vars[version].set, 
                               f"正在进行回测: {i - (total_periods - backtest_periods) + 1}/{backtest_periods} ({progress:.1f}%)")
            overall_hit_rate = sum(hit_rates) / len(hit_rates)
            full_hit_rate = full_hit_count / len(hit_rates)
            self.root.after(0, self._update_backtest_display, backtest_results, overall_hit_rate, full_hit_rate, {}, version)
        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"回测过程中发生错误: {str(e)}")
        finally:
            self.root.after(0, self.backtest_status_vars[version].set, "回测完成")
    
    def get_current_params(self, version):
        """获取当前使用的参数"""
        return self.default_params[8].copy()

    def _update_backtest_display(self, results, overall_hit_rate, full_hit_rate, pair_stats, version):
        # 清除现有结果
        for widget in self.backtest_scrollable_frames[version].winfo_children():
            widget.destroy()
        
        # 外层主Frame，左右结构（左：详细回测结果，右：投资收益统计）
        main_lr_frame = tk.Frame(self.backtest_scrollable_frames[version], bg="white")
        main_lr_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：详细回测结果
        left_frame = tk.Frame(main_lr_frame, bg="white")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 右侧：投资收益统计
        right_frame = tk.Frame(main_lr_frame, bg="white")
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False)
        
        # 详细回测结果（左侧）
        detail_label = tk.Label(left_frame, text="详细回测结果", font=("SimHei", 14, "bold"), bg="white", fg="#2196F3")
        detail_label.pack(pady=5)
        table_frame = tk.Frame(left_frame, bg="white")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建表头
        headers = ["期号", "实际开奖", "千位", "百位", "十位", "个位", "命中率", "4位全中", "操作"]
        for col, name in enumerate(headers):
            header = tk.Label(table_frame, text=name, font=("SimHei", 10, "bold"),
                             bg="#e0e0e0", relief=tk.RAISED, bd=1, width=10)
            header.grid(row=0, column=col, sticky="nsew")
        for col in range(len(headers)):
            table_frame.grid_columnconfigure(col, weight=1)
        for row, result in enumerate(results):
            tk.Label(table_frame, text=str(result['period']), font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=0, sticky="nsew")
            actual_text = " ".join(map(str, result['actual']))
            tk.Label(table_frame, text=actual_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=1, sticky="nsew")
            pred_texts = []
            for pos in range(4):
                predicted_nums = sorted(result['predictions'][pos])
                predicted_text = "".join(map(str, predicted_nums))
                pred_texts.append(predicted_text)
                bg_color = "#90EE90" if result['position_hits'][pos] else "#FFCCCC"
                tk.Label(table_frame, text=predicted_text, font=("SimHei", 8),
                         bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=2+pos, sticky="nsew")
            hit_rate_text = f"{result['hit_rate']*100:.1f}%"
            tk.Label(table_frame, text=hit_rate_text, font=("SimHei", 9),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=6, sticky="nsew")
            full_hit_text = "是" if all(result['position_hits']) else "否"
            bg_color = "#90EE90" if all(result['position_hits']) else "#FFCCCC"
            tk.Label(table_frame, text=full_hit_text, font=("SimHei", 9),
                    bg=bg_color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=7, sticky="nsew")
            # 每行末尾复制按钮
            def create_row_copy_callback(texts):
                def copy_callback():
                    self.root.clipboard_clear()
                    self.root.clipboard_append(texts)
                    self.root.update()
                return copy_callback
            row_copy_text = f"千位：{pred_texts[0]}\n百位：{pred_texts[1]}\n十位：{pred_texts[2]}\n个位：{pred_texts[3]}"
            copy_btn = tk.Button(table_frame, text="复制", font=("SimHei", 8, "bold"), width=6,
                                  command=create_row_copy_callback(row_copy_text),
                                  bg="#2196F3", fg="white", relief=tk.RAISED, padx=2, pady=1)
            copy_btn.grid(row=row+1, column=8, sticky="nsew", padx=2)
        
        # 更新Canvas的滚动区域
        self.backtest_canvases[version].configure(scrollregion=self.backtest_canvases[version].bbox("all"))
        # 横向自适应（如果内容超出宽度则可滚动）
        table_frame.update_idletasks()
        canvas = self.backtest_canvases[version]
        if table_frame.winfo_reqwidth() > canvas.winfo_width():
            canvas.configure(scrollregion=canvas.bbox("all"), width=table_frame.winfo_reqwidth())
        
        # 右侧：投资收益统计及相关信息
        params_text = "回测参数: " + ", ".join([f"{k}={v:.2f}" for k, v in self.get_current_params(version).items()])
        params_label = tk.Label(right_frame, text=params_text, font=("SimHei", 10), bg="white", pady=5)
        params_label.pack(fill=tk.X)
        stats_frame = tk.Frame(right_frame, bg="white")
        stats_frame.pack(fill=tk.X, pady=10)
        tk.Label(stats_frame, text=f"回测期数: {len(results)}", font=("SimHei", 12, "bold"), bg="white").pack(side=tk.LEFT, padx=20)
        tk.Label(stats_frame, text=f"平均命中率: {overall_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="white").pack(side=tk.LEFT, padx=20)
        tk.Label(stats_frame, text=f"4位全中率: {full_hit_rate*100:.2f}%", font=("SimHei", 12, "bold"), bg="white", fg="#FF5722").pack(side=tk.LEFT, padx=20)
        investment_stats_frame = tk.Frame(right_frame, bg="white")
        investment_stats_frame.pack(fill=tk.X, pady=10)
        tk.Label(investment_stats_frame, text="投资收益统计", font=("SimHei", 14, "bold"), bg="white", fg="#4CAF50").pack(pady=5)
        
        # 计算投资收益数据
        total_periods = len(results)
        full_hit_count = sum(1 for result in results if all(result['position_hits']))
        non_full_hit_count = total_periods - full_hit_count
        
        # 根据版本设置默认倍数和计算成本
        default_multiplier = 0.2
        numbers_per_position = version  # 每个位置选择的数字数量
        single_bet_cost = numbers_per_position ** 4 * default_multiplier  # 成本 = 数字数量^4 * 倍数
        total_cost = single_bet_cost * total_periods  # 总成本
        
        # 中奖金额计算
        single_win_amount = 9300 * default_multiplier  # 单次中奖金额
        total_win_amount = single_win_amount * full_hit_count  # 总中奖金额
        
        # 利润计算
        total_profit = total_win_amount - total_cost
        
        # 胜率计算
        win_rate = (full_hit_count / total_periods) * 100 if total_periods > 0 else 0
        
        # 利润率计算
        profit_rate = (total_profit / total_cost) * 100 if total_cost > 0 else 0
        
        # 创建投资收益统计表格
        investment_table_frame = tk.Frame(investment_stats_frame, bg="white")
        investment_table_frame.pack(fill=tk.X, padx=20)
        
        # 表头
        investment_headers = ["项目", "数值"]
        for col, header in enumerate(investment_headers):
            tk.Label(investment_table_frame, text=header, font=("SimHei", 12, "bold"),
                    bg="#e0e0e0", relief=tk.RAISED, bd=1, width=20).grid(row=0, column=col, sticky="nsew")
        
        # 统计数据
        investment_data = [
            ("回测期数", f"{total_periods}期"),
            ("全中次数", f"{full_hit_count}次"),
            ("非全中次数", f"{non_full_hit_count}次"),
            ("投注倍数", f"{default_multiplier}倍"),
            ("每期成本", f"{single_bet_cost:.2f}元"),
            ("总成本", f"{total_cost:.2f}元"),
            ("单次中奖金额", f"{single_win_amount:.2f}元"),
            ("总中奖金额", f"{total_win_amount:.2f}元"),
            ("总利润", f"{total_profit:.2f}元"),
            ("胜率", f"{win_rate:.2f}%"),
            ("利润率", f"{profit_rate:.2f}%")
        ]
        
        # 填充投资收益数据
        for row, (item, value) in enumerate(investment_data):
            # 项目名称
            tk.Label(investment_table_frame, text=item, font=("SimHei", 11),
                    bg="white", relief=tk.SUNKEN, bd=1).grid(row=row+1, column=0, sticky="nsew")
            
            # 数值，根据盈亏情况设置颜色
            if item == "总利润":
                color = "#4CAF50" if total_profit >= 0 else "#f44336"
            elif item == "利润率":
                color = "#4CAF50" if profit_rate >= 0 else "#f44336"
            elif item == "胜率":
                color = "#4CAF50" if win_rate >= 50 else "#FF9800" if win_rate >= 30 else "#f44336"
            else:
                color = "black"
            
            tk.Label(investment_table_frame, text=value, font=("SimHei", 11),
                    bg="white", fg=color, relief=tk.SUNKEN, bd=1).grid(row=row+1, column=1, sticky="nsew")
        
        # 设置列权重
        for col in range(2):
            investment_table_frame.grid_columnconfigure(col, weight=1)
        
        # 添加投资建议
        suggestion_frame = tk.Frame(investment_stats_frame, bg="white")
        suggestion_frame.pack(fill=tk.X, padx=20, pady=10)
        
        if profit_rate >= 0:
            suggestion_text = f"📈 投资建议：8位大奖策略盈利，建议继续使用"
            suggestion_color = "#4CAF50"
        elif profit_rate >= -20:
            suggestion_text = f"⚠️ 投资建议：8位大奖策略小幅亏损，建议优化参数"
            suggestion_color = "#FF9800"
        else:
            suggestion_text = f"📉 投资建议：8位大奖策略亏损较大，建议重新评估"
            suggestion_color = "#f44336"
        
        tk.Label(suggestion_frame, text=suggestion_text, font=("SimHei", 12, "bold"),
                bg="white", fg=suggestion_color).pack()
        
        # 添加两两组合统计表格（只对非版本8显示）
        if version != 8 and pair_stats:
            pair_stats_frame = tk.Frame(self.backtest_scrollable_frames[version], bg="white")
            pair_stats_frame.pack(fill=tk.X, pady=10)
            
            tk.Label(pair_stats_frame, text="两两组合命中率统计", font=("SimHei", 14, "bold"), bg="white", fg="#2196F3").pack(pady=5)
            
            # 创建两两组合统计表格
            pair_table_frame = tk.Frame(pair_stats_frame, bg="white")
            pair_table_frame.pack(fill=tk.X, padx=20)
            
            # 表头
            pair_headers = ["组合", "命中次数", "总次数", "命中率", "状态"]
            for col, header in enumerate(pair_headers):
                tk.Label(pair_table_frame, text=header, font=("SimHei", 10, "bold"),
                        bg="#e0e0e0", relief=tk.RAISED, bd=1, width=12).grid(row=0, column=col, sticky="nsew")
            
            # 组合名称映射
            pair_name_map = {
                '千百': '千位-百位', '千十': '千位-十位', '千个': '千位-个位',
                '百十': '百位-十位', '百个': '百位-个位',
                '十个': '十位-个位'
            }
            
            # 填充两两组合数据
            row = 1
            for pair_key, stats in pair_stats.items():
                hit_count = stats['hit_count']
                total_count = stats['total_count']
                hit_rate = hit_count / total_count if total_count > 0 else 0
                
                # 组合名称
                display_name = pair_name_map.get(pair_key, pair_key)
                tk.Label(pair_table_frame, text=display_name, font=("SimHei", 10),
                        bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=0, sticky="nsew")
                
                # 命中次数
                tk.Label(pair_table_frame, text=str(hit_count), font=("SimHei", 10),
                        bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=1, sticky="nsew")
                
                # 总次数
                tk.Label(pair_table_frame, text=str(total_count), font=("SimHei", 10),
                        bg="white", relief=tk.SUNKEN, bd=1).grid(row=row, column=2, sticky="nsew")
                
                # 命中率
                rate_text = f"{hit_rate*100:.1f}%"
                rate_color = "#90EE90" if hit_rate >= 0.7 else "#FFE082" if hit_rate >= 0.5 else "#FFCCCC"
                tk.Label(pair_table_frame, text=rate_text, font=("SimHei", 10),
                        bg=rate_color, relief=tk.SUNKEN, bd=1).grid(row=row, column=3, sticky="nsew")
                
                # 状态
                if hit_rate >= 0.7:
                    status = "优秀"
                    status_color = "#4CAF50"
                elif hit_rate >= 0.5:
                    status = "良好"
                    status_color = "#FF9800"
                else:
                    status = "一般"
                    status_color = "#f44336"
                
                tk.Label(pair_table_frame, text=status, font=("SimHei", 10),
                        bg="white", fg=status_color, relief=tk.SUNKEN, bd=1).grid(row=row, column=4, sticky="nsew")
                
                row += 1
            
            # 设置列权重
            for col in range(5):
                pair_table_frame.grid_columnconfigure(col, weight=1)
        
        # 添加分隔线
        separator = tk.Frame(self.backtest_scrollable_frames[version], height=2, bg="#cccccc")
        separator.pack(fill=tk.X, pady=10)
    
    def _update_result_display(self, num_periods, version):
        """更新预测结果显示"""
        # 清除现有结果
        for widget in self.result_frames[version].winfo_children():
            widget.destroy()
        
        # 显示当前使用的参数
        params_text = "当前参数: " + ", ".join([f"{k}={v:.2f}" for k, v in self.get_current_params(version).items()])
        params_label = tk.Label(self.result_frames[version], text=params_text, font=("SimHei", 10), bg="white", pady=5)
        params_label.pack(fill=tk.X)
        
        # 计算预测期号
        if self.last_period is not None:
            # 尝试解析期号格式
            try:
                if isinstance(self.last_period, str):
                    # 处理类似 "2023001" 或 "第2023001期" 格式
                    import re
                    match = re.search(r'\d+', self.last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = self.last_period[:self.last_period.index(number_part)]
                        suffix = self.last_period[self.last_period.index(number_part)+len(number_part):]
                        next_number = int(number_part) + 1
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        # 无法解析，直接加1
                        next_period = str(int(self.last_period) + 1)
                else:
                    next_period = self.last_period + 1
            except:
                next_period = f"{self.last_period + 1}"
            
            # 显示预测期号
            period_label = tk.Label(self.result_frames[version], 
                                  text=f"8位大奖 - 预测期号: {next_period}",
                                  font=("SimHei", 14, "bold"), bg="white", pady=10, fg="#FF5722")
            period_label.pack()
        
        # 显示基本信息
        info_label = tk.Label(self.result_frames[version], text=f"已分析 {num_periods} 期历史数据",
                             font=("SimHei", 12, "bold"), bg="white", pady=10)
        info_label.pack()
        
        # 创建结果表格
        position_names = ['千位', '百位', '十位', '个位', '球五']
        
        # 创建表格框架
        table_frame = tk.Frame(self.result_frames[version], bg="white")
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建表头
        for col, name in enumerate(["位置", "推荐数字", "操作", "热度指数", "遗漏期数"]):
            header = tk.Label(table_frame, text=name, font=("SimHei", 10, "bold"),
                             bg="#e0e0e0", relief=tk.RAISED, bd=1, width=20 if name=="推荐数字" else 10)
            header.grid(row=0, column=col, sticky="nsew")
        for col in range(5):
            table_frame.grid_columnconfigure(col, weight=1)
        for row, pos in enumerate(range(5)):
            pos_label = tk.Label(table_frame, text=position_names[pos], font=("SimHei", 10),
                                bg="white", relief=tk.SUNKEN, bd=1)
            pos_label.grid(row=row+1, column=0, sticky="nsew")
            nums = sorted(self.predictions[version][pos])
            nums_str = "".join(map(str, nums))
            nums_label = tk.Label(table_frame, text=nums_str, font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            nums_label.grid(row=row+1, column=1, sticky="nsew")
            # 复制按钮样式更小巧
            def create_copy_callback(numbers):
                def copy_callback():
                    self.root.clipboard_clear()
                    self.root.clipboard_append(numbers)
                    self.root.update()
                return copy_callback
            copy_button = tk.Button(table_frame, text="复制", font=("SimHei", 8), width=5,
                                  command=create_copy_callback(nums_str),
                                  bg="#e0e0e0", fg="#333333", relief=tk.GROOVE, padx=2, pady=1, bd=1, highlightthickness=0)
            copy_button.grid(row=row+1, column=2, sticky="nsew", padx=2)
            heat_data = self.models[version].heat_index[pos]
            heat_str = ", ".join([f"{i}:{heat_data[i]:.2f}" for i in nums])
            heat_label = tk.Label(table_frame, text=heat_str, font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            heat_label.grid(row=row+1, column=3, sticky="nsew")
            miss_data = self.models[version].miss_counts[pos]
            miss_str = ", ".join([f"{i}:{int(miss_data[i])}" for i in nums])
            miss_label = tk.Label(table_frame, text=miss_str, font=("SimHei", 10),
                                 bg="white", relief=tk.SUNKEN, bd=1)
            miss_label.grid(row=row+1, column=4, sticky="nsew")
        
        # 创建一个复制所有按钮
        def copy_all_numbers():
            position_names = ['千位', '百位', '十位', '个位']  # 只包含前四位
            all_numbers = ""
            for pos in range(4):  # 只循环前四位
                nums = sorted(self.predictions[version][pos])
                nums_str = "".join(map(str, nums))
                all_numbers += f"{position_names[pos]}：{nums_str}\n"  # 添加位置名称
            self.root.clipboard_clear()
            self.root.clipboard_append(all_numbers.strip())
            self.root.update()
        
        copy_all_button = tk.Button(self.result_frames[version], text="复制所有推荐数字", font=("SimHei", 10, "bold"),
                                  command=copy_all_numbers, bg="#2196F3", fg="white", pady=5)
        copy_all_button.pack(pady=10)
    
    def _create_statistics_charts(self, version):
        # 历史统计已移除，不再显示任何内容
        pass
    
    def show_params_dialog(self, version):
        """显示参数设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"{version}位大奖 - 模型参数设置")
        dialog.geometry("400x400")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建参数输入框
        param_entries = {}
        param_labels = {
            'alpha': '平滑因子 (alpha):',
            'lambda': '冷号衰减系数 (lambda):',
            'short_weight': '短期预测权重:',
            'long_weight': '长期预测权重:',
            'co_weight': '协同预测权重:',
            'hot_threshold': '热号阈值:',
            'cold_threshold': '冷号阈值:'
        }
        
        # 获取当前参数值
        current_params = self.get_current_params(version)
        
        frame = tk.Frame(dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        for i, (param, label_text) in enumerate(param_labels.items()):
            tk.Label(frame, text=label_text, font=("SimHei", 10)).grid(row=i, column=0, sticky=tk.W, pady=5)
            
            var = tk.DoubleVar(value=current_params[param])
            entry = tk.Entry(frame, textvariable=var, width=10)
            entry.grid(row=i, column=1, sticky=tk.W, pady=5)
            
            param_entries[param] = var
        
        # 添加说明文本
        help_text = "提示:\n" \
                   "- 权重参数之和应为1\n" \
                   "- 调整参数后需重新回测和预测\n" \
                   "- 参数将自动保存到配置文件"
        tk.Label(frame, text=help_text, font=("SimHei", 9), fg="blue", justify=tk.LEFT).grid(
            row=len(param_labels), column=0, columnspan=2, sticky=tk.W, pady=10)
        
        # 按钮框架
        btn_frame = tk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 确定按钮
        def save_params():
            new_params = {}
            for param, var in param_entries.items():
                try:
                    value = float(var.get())
                    new_params[param] = value
                except ValueError:
                    messagebox.showerror("参数错误", f"{param_labels[param]}必须是数字")
                    return
            
            # 验证权重之和是否接近1
            weights = [new_params['short_weight'], new_params['long_weight'], new_params['co_weight']]
            if abs(sum(weights) - 1.0) > 0.01:
                messagebox.showwarning("权重警告", "三种预测方法的权重之和应接近1.0")
            
            # 保存参数到配置文件
            self.configs[version].update(new_params)
            if self.config_managers[version].save_config(self.configs[version]):
                # 更新当前参数
                self.default_params[version] = new_params
                messagebox.showinfo("参数设置", f"{version}位大奖参数已保存到配置文件，下次预测将使用新参数")
                dialog.destroy()
            else:
                messagebox.showerror("保存失败", "参数保存到配置文件失败")
        
        tk.Button(btn_frame, text="确定", command=save_params, bg="#4CAF50", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        # 重置按钮
        def reset_params():
            for param, var in param_entries.items():
                var.set(DEFAULT_CONFIG[param])
        
        tk.Button(btn_frame, text="重置默认", command=reset_params, bg="#FF9800", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="取消", command=dialog.destroy, bg="#f44336", fg="white", 
                 padx=15).pack(side=tk.LEFT, padx=5)
    
    def show_add_result_dialog(self, edit_period=None):
        """显示添加/修改开奖结果的对话框"""
        try:
            df = pd.read_excel(self.data_file)
            if not all(col in df.columns for col in ['期号', '千位', '百位', '十位', '个位', '球五']):
                messagebox.showerror("错误", "data.xlsx文件格式不正确，请确保包含所有必要的列")
                return
            
            # 确保期号列为字符串类型
            df['期号'] = df['期号'].astype(str)
            
        except Exception as e:
            messagebox.showerror("错误", f"无法读取data.xlsx文件: {str(e)}")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title("修改开奖号码" if edit_period else "录入开奖号码")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建输入框架
        input_frame = tk.Frame(dialog, padx=20, pady=10)
        input_frame.pack(fill=tk.BOTH, expand=True)
        
        # 期号输入
        period_frame = tk.Frame(input_frame)
        period_frame.pack(fill=tk.X, pady=5)
        tk.Label(period_frame, text="期号:", font=("SimHei", 10)).pack(side=tk.LEFT)
        period_var = tk.StringVar()
        
        if edit_period:
            # 修改模式：设置期号并禁用输入
            period_var.set(edit_period)
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15, state='readonly')
            # 获取当前号码
            row = df[df['期号'] == str(edit_period)].iloc[0]
            current_numbers = [row['千位'], row['百位'], row['十位'], row['个位'], row['球五']]
        else:
            # 添加模式：计算下一期号
            period_entry = tk.Entry(period_frame, textvariable=period_var, width=15)
            try:
                last_period = str(df['期号'].iloc[-1])
                if last_period:
                    import re
                    match = re.search(r'\d+', last_period)
                    if match:
                        number_part = match.group(0)
                        prefix = last_period[:last_period.index(number_part)]
                        suffix = last_period[last_period.index(number_part)+len(number_part):]
                        next_number = str(int(number_part) + 1).zfill(len(number_part))
                        next_period = f"{prefix}{next_number}{suffix}"
                    else:
                        next_period = str(int(last_period) + 1)
                    period_var.set(next_period)
            except:
                pass
            current_numbers = ['' for _ in range(5)]
            
        period_entry.pack(side=tk.LEFT, padx=5)
        
        # 号码输入框架
        numbers_frame = tk.Frame(input_frame)
        numbers_frame.pack(fill=tk.X, pady=10)
        
        # 创建5个数字输入框
        number_vars = []
        number_entries = []
        position_names = ['千位', '百位', '十位', '个位', '球五']
        
        for i, pos_name in enumerate(position_names):
            pos_frame = tk.Frame(numbers_frame)
            pos_frame.pack(pady=5)
            
            tk.Label(pos_frame, text=f"{pos_name}:", font=("SimHei", 10)).pack(side=tk.LEFT)
            var = tk.StringVar(value=str(current_numbers[i]))
            number_vars.append(var)
            entry = tk.Entry(pos_frame, textvariable=var, width=5)
            entry.pack(side=tk.LEFT, padx=5)
            number_entries.append(entry)
            
            # 添加验证
            def validate_number(P):
                if P == "": return True
                return P.isdigit() and len(P) <= 1 and 0 <= int(P) <= 9
            vcmd = (dialog.register(validate_number), '%P')
            entry.config(validate='key', validatecommand=vcmd)
        
        # 设置焦点移动
        def focus_next(event):
            current = dialog.focus_get()
            if current in number_entries:
                idx = number_entries.index(current)
                if idx < len(number_entries) - 1:
                    number_entries[idx + 1].focus()
                    return "break"
            return None
            
        for entry in number_entries:
            entry.bind('<Key-Return>', focus_next)
            entry.bind('<Key-Tab>', focus_next)
        
        # 保存按钮
        def save_result():
            # 验证输入
            if not period_var.get().strip():
                messagebox.showwarning("警告", "请输入期号")
                return
                
            numbers = []
            for var in number_vars:
                if not var.get().strip():
                    messagebox.showwarning("警告", "请输入完整的开奖号码")
                    return
                try:
                    num = int(var.get())
                    if not (0 <= num <= 9):
                        raise ValueError
                    numbers.append(num)
                except ValueError:
                    messagebox.showwarning("警告", "号码必须是0-9之间的数字")
                    return
            
            try:
                # 读取现有Excel文件
                df = pd.read_excel(self.data_file)
                
                # 确保期号列为字符串类型
                df['期号'] = df['期号'].astype(str)
                current_period = str(period_var.get()).strip()
                
                if edit_period:
                    # 修改模式：更新现有行
                    mask = df['期号'] == current_period
                    df.loc[mask, ['千位', '百位', '十位', '个位', '球五']] = numbers
                else:
                    # 添加模式：检查期号是否已存在
                    if current_period in df['期号'].values:
                        messagebox.showerror("错误", "该期号已存在，每个期号必须是唯一的")
                        return
                    
                    # 创建新行数据
                    new_data = {
                        '期号': current_period,
                        '千位': numbers[0],
                        '百位': numbers[1],
                        '十位': numbers[2],
                        '个位': numbers[3],
                        '球五': numbers[4]
                    }
                    
                    # 直接使用loc添加新行到最后
                    df.loc[len(df)] = new_data
                
                # 保存到Excel，保持原有顺序
                df.to_excel(self.data_file, index=False)
                
                messagebox.showinfo("成功", "开奖号码已保存")
                dialog.destroy()
                
                # 重新加载数据
                self.file_path_var.set(self.data_file)
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
        
        btn_frame = tk.Frame(input_frame)
        btn_frame.pack(fill=tk.X, pady=20)
        
        save_btn = tk.Button(btn_frame, text="保存", command=save_result,
                           bg="#4CAF50", fg="white", font=("SimHei", 10),
                           relief=tk.RAISED, padx=20)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(btn_frame, text="取消", command=dialog.destroy,
                             bg="#f44336", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 设置初始焦点
        if edit_period:
            number_entries[0].focus()
        else:
            if not period_var.get():
                period_entry.focus()
            else:
                number_entries[0].focus()
    
    def show_edit_result_dialog(self):
        """显示修改开奖号码的对话框"""
        data_file = self.data_file
        try:
            df = pd.read_excel(data_file)
            df['期号'] = df['期号'].astype(str)
        except Exception as e:
            messagebox.showerror("错误", f"无法读取data.xlsx文件: {str(e)}")
            return
        
        # 创建期号选择对话框
        select_dialog = tk.Toplevel(self.root)
        select_dialog.title("选择要修改的期号")
        select_dialog.geometry("300x400")
        select_dialog.resizable(False, False)
        select_dialog.transient(self.root)
        select_dialog.grab_set()
        
        # 创建列表框架
        frame = tk.Frame(select_dialog, padx=20, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动条
        scrollbar = tk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建列表框
        listbox = tk.Listbox(frame, yscrollcommand=scrollbar.set, font=("SimHei", 10))
        listbox.pack(fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=listbox.yview)
        
        # 添加期号到列表
        periods = df['期号'].tolist()
        for period in reversed(periods):  # 倒序显示，最新的在最上面
            listbox.insert(tk.END, period)
        
        def on_select():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择要修改的期号")
                return
            period = listbox.get(selection[0])
            select_dialog.destroy()
            self.show_add_result_dialog(edit_period=period)
        
        # 按钮框架
        btn_frame = tk.Frame(select_dialog)
        btn_frame.pack(fill=tk.X, pady=10)
        
        select_btn = tk.Button(btn_frame, text="修改", command=on_select,
                             bg="#4CAF50", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        select_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(btn_frame, text="取消", command=select_dialog.destroy,
                             bg="#f44336", fg="white", font=("SimHei", 10),
                             relief=tk.RAISED, padx=20)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 双击选择
        listbox.bind('<Double-Button-1>', lambda e: on_select())
        
        # 设置初始焦点
        listbox.focus_set()
        if listbox.size() > 0:
            listbox.select_set(0)
    
    def auto_optimize_params(self, version):
        """自动优化参数"""
        if self.history_data is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        dialog = tk.Toplevel(self.root)
        dialog.title(f"{version}位大奖 - 自动优化参数")
        dialog.geometry("800x600")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        left_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        right_frame = tk.Frame(main_frame, relief=tk.GROOVE, bd=2)
        right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        title_frame = tk.Frame(left_frame, bg="#4CAF50")
        title_frame.pack(fill=tk.X)
        tk.Label(title_frame, text="优化设置", font=("SimHei", 12, "bold"), 
                bg="#4CAF50", fg="white", pady=5).pack()
        params_frame = tk.Frame(left_frame, padx=20, pady=10)
        params_frame.pack(side=tk.TOP, fill=tk.X, expand=False)
        param_frame1 = tk.Frame(params_frame)
        param_frame1.pack(fill=tk.X, pady=5)
        tk.Label(param_frame1, text="回测期数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        backtest_periods_var = tk.IntVar(value=30)
        backtest_periods_entry = tk.Entry(param_frame1, textvariable=backtest_periods_var, width=10)
        backtest_periods_entry.pack(side=tk.LEFT, padx=5)
        tk.Label(param_frame1, text="回撤天数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        optimize_rollback_var = tk.IntVar(value=0)
        optimize_rollback_entry = tk.Entry(param_frame1, textvariable=optimize_rollback_var, width=10)
        optimize_rollback_entry.pack(side=tk.LEFT, padx=5)
        param_frame2 = tk.Frame(params_frame)
        param_frame2.pack(fill=tk.X, pady=5)
        tk.Label(param_frame2, text="目标命中率(%):", font=("SimHei", 10)).pack(side=tk.LEFT)
        target_hit_rate_var = tk.DoubleVar(value=85.0)
        target_hit_rate_entry = tk.Entry(param_frame2, textvariable=target_hit_rate_var, width=10)
        target_hit_rate_entry.pack(side=tk.LEFT, padx=5)
        param_frame3 = tk.Frame(params_frame)
        param_frame3.pack(fill=tk.X, pady=5)
        tk.Label(param_frame3, text="最大迭代次数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        max_iterations_var = tk.IntVar(value=50)
        max_iterations_entry = tk.Entry(param_frame3, textvariable=max_iterations_var, width=10)
        max_iterations_entry.pack(side=tk.LEFT, padx=5)
        param_frame4 = tk.Frame(params_frame)
        param_frame4.pack(fill=tk.X, pady=5)
        tk.Label(param_frame4, text="种群大小:", font=("SimHei", 10)).pack(side=tk.LEFT)
        population_size_var = tk.IntVar(value=50)
        population_size_entry = tk.Entry(param_frame4, textvariable=population_size_var, width=10)
        population_size_entry.pack(side=tk.LEFT, padx=5)
        param_frame5 = tk.Frame(params_frame)
        param_frame5.pack(fill=tk.X, pady=5)
        tk.Label(param_frame5, text="线程数:", font=("SimHei", 10)).pack(side=tk.LEFT)
        num_threads_var = tk.IntVar(value=4)
        num_threads_entry = tk.Entry(param_frame5, textvariable=num_threads_var, width=10)
        num_threads_entry.pack(side=tk.LEFT, padx=5)
        progress_frame = tk.Frame(left_frame, padx=20, pady=10)
        progress_frame.pack(side=tk.TOP, fill=tk.X, expand=False)
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, mode='determinate')
        progress_bar.pack(fill=tk.X, pady=5)
        iteration_var = tk.StringVar(value="准备优化...")
        iteration_label = tk.Label(progress_frame, textvariable=iteration_var, font=("SimHei", 10))
        iteration_label.pack(pady=5)
        runtime_var = tk.StringVar(value="运行时间: 0秒")
        runtime_label = tk.Label(progress_frame, textvariable=runtime_var, font=("SimHei", 10))
        runtime_label.pack(pady=5)
        remaining_time_var = tk.StringVar(value="预计剩余时间: 计算中...")
        remaining_time_label = tk.Label(progress_frame, textvariable=remaining_time_var, font=("SimHei", 10))
        remaining_time_label.pack(pady=5)
        best_result_var = tk.StringVar(value="当前最佳命中率: 0.00%")
        best_result_label = tk.Label(progress_frame, textvariable=best_result_var, font=("SimHei", 10))
        best_result_label.pack(pady=5)
        # 右侧优化日志等原有内容不变 ...
        # 修复nonlocal声明
        optimization_running = False
        current_thread = None
        current_optimizer = None
        start_time = None
        # 日志区，必须在所有函数和按钮定义之前
        log_text = tk.Text(right_frame, height=20, width=40, font=("SimHei", 9),
                          wrap=tk.WORD, bg="#f5f5f5", relief=tk.SUNKEN)
        log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        # 先定义按钮功能函数
        def run_optimization():
            nonlocal current_optimizer, optimization_running, current_thread, start_time, log_text
            try:
                try:
                    backtest_periods = int(backtest_periods_entry.get())
                    rollback_days = int(optimize_rollback_var.get())
                    target_hit_rate = float(target_hit_rate_entry.get()) / 100
                    max_iterations = int(max_iterations_entry.get())
                    population_size = int(population_size_entry.get())
                    num_threads = int(num_threads_entry.get())
                except ValueError as e:
                    messagebox.showerror("错误", "请输入有效的数值")
                    return
                if backtest_periods <= 0:
                    messagebox.showwarning("警告", "回测期数必须大于0")
                    return
                if rollback_days < 0:
                    messagebox.showwarning("警告", "回撤天数不能为负数")
                    return
                if backtest_periods + rollback_days >= len(self.history_data):
                    messagebox.showwarning("警告", "回测期数+回撤天数不能大于历史数据总数")
                    return
                if rollback_days > 0:
                    data = self.history_data[:-rollback_days]
                else:
                    data = self.history_data
                log_text.delete(1.0, tk.END)
                def add_log(message):
                    dialog.after(0, lambda: log_text.insert(tk.END, f"{message}\n"))
                    dialog.after(0, lambda: log_text.see(tk.END))
                    dialog.after(0, log_text.update)
                add_log("正在初始化优化过程...")
                add_log(f"使用 {num_threads} 个线程进行并行计算")
                for widget in [backtest_periods_entry, optimize_rollback_entry, target_hit_rate_entry, max_iterations_entry, population_size_entry, num_threads_entry, start_button]:
                    widget.config(state='disabled')
                stop_button.config(state='normal')
                progress_var.set(0)
                iteration_var.set("准备开始优化...")
                best_result_var.set("当前最佳命中率: 0.00%")
                runtime_var.set("运行时间: 0秒")
                remaining_time_var.set("预计剩余时间: 计算中...")
                optimization_running = True
                start_time = time.time()
                current_optimizer = ParameterOptimizer(
                    target_hit_rate=target_hit_rate,
                    max_iterations=max_iterations,
                    population_size=population_size,
                    num_threads=num_threads,
                    period=backtest_periods
                )
                def evaluate_params(params):
                    if not optimization_running:
                        return 0
                    total_periods = len(data)
                    if backtest_periods > total_periods:
                        return 0
                    total_hits = 0
                    position_pairs = [
                        (0, 1), (0, 2), (0, 3), (1, 2), (1, 3), (2, 3)
                    ]
                    pair_hit_counts = [0] * len(position_pairs)
                    for i in range(total_periods - backtest_periods, total_periods):
                        train_data = data[:i]
                        model = MFTNModel(params, version)
                        model.fit(train_data)
                        predictions = model.predict_next()
                        position_hits = []
                        for pos in range(4):
                            predicted_nums = predictions[pos]
                            actual_num = data[i][pos]
                            hit = actual_num in predicted_nums
                            position_hits.append(hit)
                        if version != 8:
                            for pair_idx, (pos1, pos2) in enumerate(position_pairs):
                                if position_hits[pos1] and position_hits[pos2]:
                                    pair_hit_counts[pair_idx] += 1
                        if all(position_hits):
                            total_hits += 1
                    overall_hit_rate = total_hits / backtest_periods
                    if version == 8:
                        return overall_hit_rate
                    else:
                        pair_hit_rates = [count / backtest_periods for count in pair_hit_counts]
                        avg_pair_hit_rate = sum(pair_hit_rates) / len(pair_hit_rates)
                        final_score = 0.7 * overall_hit_rate + 0.3 * avg_pair_hit_rate
                        return final_score
                def optimize_process():
                    try:
                        add_log("开始优化过程...")
                        add_log(f"回测期数: {backtest_periods}")
                        add_log(f"目标命中率: {current_optimizer.target_hit_rate*100:.1f}%")
                        add_log(f"最大迭代次数: {current_optimizer.max_iterations}")
                        add_log(f"使用 {current_optimizer.num_threads} 个线程进行并行计算")
                        add_log("-" * 30)
                        best_params, best_hit_rate = current_optimizer.optimize(evaluate_params)
                        if optimization_running and best_params:
                            result_text = f"参数优化完成!\n" \
                                         f"最佳命中率: {best_hit_rate*100:.2f}%\n\n" \
                                         f"最佳参数:\n" + \
                                         "\n".join([f"{k} = {v:.3f}" for k, v in best_params.items()])
                            add_log("\n优化完成!")
                            add_log(result_text)
                            dialog.after(0, lambda: apply_button.config(state='normal'))
                            dialog.after(0, messagebox.showinfo, "优化完成", result_text)
                    except Exception as e:
                        error_message = f"优化过程中发生错误: {str(e)}"
                        add_log(f"\n错误: {error_message}")
                        dialog.after(0, messagebox.showerror, "优化错误", error_message)
                    finally:
                        dialog.after(0, lambda: [
                            widget.config(state='normal')
                            for widget in [backtest_periods_entry, optimize_rollback_entry, target_hit_rate_entry, max_iterations_entry, population_size_entry, num_threads_entry, start_button]
                        ])
                        dialog.after(0, lambda: stop_button.config(state='disabled'))
                current_thread = threading.Thread(target=optimize_process)
                current_thread.daemon = True
                current_thread.start()
                # 实时刷新运行时间、剩余时间、迭代次数
                def update_runtime():
                    nonlocal start_time
                    if optimization_running and start_time is not None:
                        current_time = time.time()
                        elapsed_seconds = int(current_time - start_time)
                        minutes = elapsed_seconds // 60
                        seconds = elapsed_seconds % 60
                        time_text = f"运行时间: {minutes}分{seconds}秒"
                        runtime_var.set(time_text)
                        # 预计剩余时间
                        if hasattr(current_optimizer, 'current_iteration') and current_optimizer.current_iteration > 0:
                            time_per_iter = elapsed_seconds / current_optimizer.current_iteration
                            remaining_iters = current_optimizer.max_iterations - current_optimizer.current_iteration
                            estimated_remaining_seconds = int(time_per_iter * remaining_iters)
                            rem_minutes = estimated_remaining_seconds // 60
                            rem_seconds = estimated_remaining_seconds % 60
                            if rem_minutes > 60:
                                rem_hours = rem_minutes // 60
                                rem_minutes = rem_minutes % 60
                                remaining_text = f"预计剩余时间: {rem_hours}小时{rem_minutes}分"
                            else:
                                remaining_text = f"预计剩余时间: {rem_minutes}分{rem_seconds}秒"
                            remaining_time_var.set(remaining_text)
                        else:
                            remaining_time_var.set("预计剩余时间: 计算中...")
                        # 迭代次数
                        if hasattr(current_optimizer, 'current_iteration'):
                            progress = (current_optimizer.current_iteration / current_optimizer.max_iterations) * 100
                            iteration_var.set(f"迭代进度: {current_optimizer.current_iteration}/{current_optimizer.max_iterations} ({progress:.1f}%)")
                        # 实时刷新当前最佳命中率
                        if hasattr(current_optimizer, 'best_hit_rate'):
                            best_result_var.set(f"当前最佳命中率: {current_optimizer.best_hit_rate*100:.2f}%")
                        dialog.after(1000, update_runtime)
                update_runtime()
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的数值")
        def stop_optimization():
            nonlocal optimization_running, current_thread
            optimization_running = False
            stop_button.config(state='disabled')
            if current_thread and current_thread.is_alive():
                try:
                    current_thread.join(timeout=3)
                except Exception as e:
                    print(f"停止线程时发生错误: {e}")
            start_button.config(state='normal')
            if current_optimizer and current_optimizer.best_params:
                apply_button.config(state='normal')
        def apply_best_params():
            if current_optimizer and current_optimizer.best_params:
                self.configs[version].update(current_optimizer.best_params)
                if self.config_managers[version].save_config(self.configs[version]):
                    self.default_params[version] = current_optimizer.best_params
                    self.save_last_optimization_date()
                    messagebox.showinfo("成功", f"{version}位大奖已应用最佳参数")
                    dialog.destroy()
                    self.optimization_reminder_shown = False
                    self.check_optimization_reminder()
                    if messagebox.askyesno("提示", f"{version}位大奖参数已更新，是否要重新运行预测？"):
                        self.run_backtest_and_predict(version)
                else:
                    messagebox.showerror("错误", "保存参数失败")
        # 恢复底部按钮区域
        btn_frame = tk.Frame(left_frame)
        btn_frame.pack(side=tk.TOP, fill=tk.X, padx=20, pady=10)
        start_button = tk.Button(btn_frame, text="开始优化", command=run_optimization,
                               font=("SimHei", 10), bg="#4CAF50", fg="white",
                               relief=tk.RAISED, padx=20)
        start_button.pack(side=tk.LEFT, padx=5)
        stop_button = tk.Button(btn_frame, text="停止优化", command=stop_optimization,
                              font=("SimHei", 10), bg="#FF9800", fg="white",
                              relief=tk.RAISED, padx=20, state='disabled')
        stop_button.pack(side=tk.LEFT, padx=5)
        apply_button = tk.Button(btn_frame, text="应用最佳参数", command=apply_best_params,
                               font=("SimHei", 10), bg="#2196F3", fg="white",
                               relief=tk.RAISED, padx=20, state='disabled')
        apply_button.pack(side=tk.LEFT, padx=5)
        close_button = tk.Button(btn_frame, text="关闭窗口", command=dialog.destroy,
                               font=("SimHei", 10), bg="#f44336", fg="white",
                               relief=tk.RAISED, padx=20)
        close_button.pack(side=tk.LEFT, padx=5)
        # ... 其余按钮和UI逻辑不变 ...
    
    def init_params(self, version):
        # 只保留8位大奖
        init_file = f"init8.json"
        if not os.path.exists(init_file):
            messagebox.showerror("错误", f"未找到初始化配置文件({init_file})")
            return
        if not messagebox.askyesno("确认", f"确定要使用初始化参数替换8位大奖的当前参数吗？\n此操作将覆盖现有的参数设置。"):
            return
        success, message = self.config_managers[8].apply_init_config()
        if success:
            self.configs[8] = self.config_managers[8].load_config()
            self.default_params[8] = {k: v for k, v in self.configs[8].items() if k != 'last_excel_file'}
            self.models[8] = MFTNModel(self.default_params[8], 8)
            messagebox.showinfo("成功", f"8位大奖的{message}")
            if self.history_data is not None:   
                if messagebox.askyesno("提示", f"8位大奖的参数已更新，是否要重新运行预测？"):
                    self.run_backtest_and_predict(8)
        else:
            messagebox.showerror("错误", f"8位大奖的{message}")
    
    def complete_initialization(self):
        """设置初始化完成标志"""
        # 先执行原有的延迟自动加载
        self.delayed_auto_load()
        # 设置初始化完成标志，允许标签页切换触发自动回测
        self.initialization_complete = True
        self.status_var.set("初始化完成，可以开始使用")
        
        # 加载上次优化日期并检查是否需要提醒
        self.load_last_optimization_date()
        self.check_optimization_reminder()

    def load_last_optimization_date(self):
        """加载上次优化日期"""
        try:
            if os.path.exists('last_optimization.json'):
                with open('last_optimization.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.last_optimization_date = data.get('last_date')
                    self.optimization_cycle_days = data.get('cycle_days', 10)
        except Exception as e:
            print(f"加载上次优化日期失败: {e}")
            self.last_optimization_date = None

    def save_last_optimization_date(self):
        """保存当前优化日期"""
        try:
            current_date = time.strftime('%Y-%m-%d')
            data = {
                'last_date': current_date,
                'cycle_days': self.optimization_cycle_days
            }
            with open('last_optimization.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.last_optimization_date = current_date
        except Exception as e:
            print(f"保存优化日期失败: {e}")

    def check_optimization_reminder(self):
        """检查是否需要显示优化提醒"""
        if self.optimization_reminder_shown:
            return
            
        current_date = time.strftime('%Y-%m-%d')
        
        if self.last_optimization_date is None:
            # 首次使用，显示欢迎信息
            self.show_optimization_reminder("欢迎使用！建议每10天优化一次参数以获得最佳效果。")
            return
        
        # 计算距离上次优化的天数
        try:
            last_date = datetime.strptime(self.last_optimization_date, '%Y-%m-%d')
            current_date_obj = datetime.strptime(current_date, '%Y-%m-%d')
            days_since_optimization = (current_date_obj - last_date).days
            
            if days_since_optimization >= self.optimization_cycle_days:
                next_optimization_date = last_date + timedelta(days=self.optimization_cycle_days)
                reminder_text = f"距离上次优化已过去{days_since_optimization}天，建议进行参数优化。\n" \
                              f"上次优化日期: {self.last_optimization_date}\n" \
                              f"建议下次优化日期: {next_optimization_date.strftime('%Y-%m-%d')}"
                self.show_optimization_reminder(reminder_text)
            else:
                days_remaining = self.optimization_cycle_days - days_since_optimization
                next_optimization_date = last_date + timedelta(days=self.optimization_cycle_days)
                status_text = f"距离下次优化还有{days_remaining}天 (上次: {self.last_optimization_date}, 下次: {next_optimization_date.strftime('%Y-%m-%d')})"
                self.update_optimization_status(status_text)
        except Exception as e:
            print(f"检查优化提醒时发生错误: {e}")

    def show_optimization_reminder(self, message):
        """显示优化提醒"""
        self.optimization_reminder_var.set(message)
        self.optimization_reminder_frame.configure(bg="#FFEBEE")  # 红色背景表示需要优化
        self.optimization_reminder_label.configure(bg="#FFEBEE", fg="#C62828")
        self.optimization_reminder_shown = True
        
        # 添加确认按钮
        if hasattr(self, 'reminder_confirm_btn'):
            self.reminder_confirm_btn.destroy()
        
        self.reminder_confirm_btn = tk.Button(self.optimization_reminder_frame, 
                                            text="我知道了", 
                                            command=self.dismiss_optimization_reminder,
                                            font=("SimHei", 8), bg="#C62828", fg="white",
                                            relief=tk.RAISED, padx=10)
        self.reminder_confirm_btn.pack(side=tk.RIGHT, padx=10, pady=5)

    def dismiss_optimization_reminder(self):
        """关闭优化提醒"""
        self.optimization_reminder_var.set("")
        self.optimization_reminder_frame.configure(bg="#FFF3CD")
        self.optimization_reminder_label.configure(bg="#FFF3CD", fg="#856404")
        if hasattr(self, 'reminder_confirm_btn'):
            self.reminder_confirm_btn.destroy()

    def update_optimization_status(self, message):
        """更新优化状态显示"""
        self.optimization_reminder_var.set(message)
        self.optimization_reminder_frame.configure(bg="#E8F5E8")  # 绿色背景表示正常
        self.optimization_reminder_label.configure(bg="#E8F5E8", fg="#2E7D32")

    def show_optimization_cycle_dialog(self, version):
        dialog = tk.Toplevel(self.root)
        dialog.title("优化周期设置")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        main_frame = tk.Frame(dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        title_label = tk.Label(main_frame, text="参数优化周期设置", 
                              font=("SimHei", 14, "bold"), fg="#2196F3")
        title_label.pack(pady=(0, 20))
        # 动态生成说明内容
        info_text = "根据量化对比实验结果，各周期优化效果如下：\n"
        for period in self.quantitative_periods:
            result = self.quantitative_results.get(period, {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0})
            avg = result.get('avg_hit_rate', 0.0) * 100
            full = result.get('full_hit_rate', 0.0) * 100
            info_text += f"• {period}天周期：平均命中率{avg:.2f}%，全中率{full:.2f}%\n"
        info_label = tk.Label(main_frame, text=info_text, 
                             font=("SimHei", 10), justify=tk.LEFT,
                             bg="#F5F5F5", padx=15, pady=15)
        info_label.pack(fill=tk.X, pady=(0, 20))
        # 周期设置
        cycle_frame = tk.Frame(main_frame)
        cycle_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(cycle_frame, text="优化周期(天):", font=("SimHei", 10)).pack(side=tk.LEFT)
        cycle_var = tk.IntVar(value=self.optimization_cycle_days)
        cycle_entry = tk.Entry(cycle_frame, textvariable=cycle_var, width=10)
        cycle_entry.pack(side=tk.LEFT, padx=10)
        
        # 当前状态
        if self.last_optimization_date:
            status_text = f"上次优化日期: {self.last_optimization_date}"
        else:
            status_text = "尚未进行过参数优化"
        
        status_label = tk.Label(main_frame, text=status_text, 
                               font=("SimHei", 10), fg="#666666")
        status_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def save_settings():
            try:
                new_cycle = int(cycle_var.get())
                if new_cycle < 1 or new_cycle > 100:
                    messagebox.showerror("错误", "优化周期必须在1-100天之间")
                    return
                
                self.optimization_cycle_days = new_cycle
                self.save_last_optimization_date()
                messagebox.showinfo("成功", f"优化周期已设置为{new_cycle}天")
                dialog.destroy()
                
                # 重新检查提醒
                self.check_optimization_reminder()
                
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
        
        def reset_to_default():
            cycle_var.set(10)
        
        save_btn = tk.Button(button_frame, text="保存设置", 
                           command=save_settings,
                           font=("SimHei", 10), bg="#4CAF50", fg="white",
                           relief=tk.RAISED, padx=15)
        save_btn.pack(side=tk.LEFT, padx=5)
        
        reset_btn = tk.Button(button_frame, text="恢复默认", 
                            command=reset_to_default,
                            font=("SimHei", 10), bg="#FF9800", fg="white",
                            relief=tk.RAISED, padx=15)
        reset_btn.pack(side=tk.LEFT, padx=5)
        
        cancel_btn = tk.Button(button_frame, text="取消", 
                             command=dialog.destroy,
                             font=("SimHei", 10), bg="#9E9E9E", fg="white",
                             relief=tk.RAISED, padx=15)
        cancel_btn.pack(side=tk.RIGHT, padx=5)

    def _create_quantitative_analysis_frame(self, parent_frame):
        """创建量化对比分析框架"""
        # --- 新增：外层Canvas和滚动条 ---
        outer_frame = tk.Frame(parent_frame, bg="white")
        outer_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        canvas = tk.Canvas(outer_frame, bg="white", highlightthickness=0)
        vscrollbar = tk.Scrollbar(outer_frame, orient="vertical", command=canvas.yview)
        hscrollbar = tk.Scrollbar(outer_frame, orient="horizontal", command=canvas.xview)
        scrollable_frame = tk.Frame(canvas, bg="white")
        # 绑定内容变化，动态调整scrollregion和宽高
        def on_configure(event, c=canvas, s=scrollable_frame):
            c.configure(scrollregion=c.bbox("all"))
            if s.winfo_reqwidth() > c.winfo_width():
                c.configure(width=s.winfo_reqwidth())
            if s.winfo_reqheight() > c.winfo_height():
                c.configure(height=s.winfo_reqheight())
        scrollable_frame.bind("<Configure>", on_configure)
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=vscrollbar.set, xscrollcommand=hscrollbar.set)
        vscrollbar.pack(side="right", fill="y")
        hscrollbar.pack(side="bottom", fill="x")
        canvas.pack(side="left", fill="both", expand=True)
        # --- 原有内容全部放到scrollable_frame ---
        analysis_frame = tk.Frame(scrollable_frame, bg="white", relief=tk.RAISED, bd=2)
        analysis_frame.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        # 标题
        title_frame = tk.Frame(analysis_frame, bg="#2196F3", height=40)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        title_label = tk.Label(title_frame, text="量化对比分析", 
                              font=("SimHei", 14, "bold"), 
                              bg="#2196F3", fg="white")
        title_label.pack(expand=True)
        # 内容区域
        content_frame = tk.Frame(analysis_frame, bg="white")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        # 说明文字
        info_text = "基于历史数据的优化周期对比实验："
        info_label = tk.Label(content_frame, text=info_text, 
                             font=("SimHei", 11, "bold"), 
                             bg="white", fg="#333333", anchor=tk.W)
        info_label.pack(fill=tk.X, pady=(0, 10))
        # 创建图表
        self._create_quantitative_charts(content_frame)
        # 详细结果表格
        self._create_quantitative_table(content_frame)
        # 操作按钮
        self._create_quantitative_buttons(content_frame)

    def _create_quantitative_charts(self, parent_frame):
        print('DEBUG: in _create_quantitative_charts, self.quantitative_results =', self.quantitative_results)
        """创建量化对比图表"""
        charts_frame = tk.Frame(parent_frame, bg="white")
        charts_frame.pack(fill=tk.X, pady=(0, 10))
        if not self.quantitative_results:
            no_data_label = tk.Label(charts_frame, text="暂无量化对比结果，请先运行分析", 
                                   font=('Arial', 10), fg='gray', bg='white')
            no_data_label.pack(pady=20)
            return
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(6, 6))
        fig.patch.set_facecolor('white')
        fig.subplots_adjust(hspace=0.5)
        periods = sorted(self.quantitative_periods)
        avg_hit_rates = []
        full_hit_rates = []
        for period in periods:
            result = self.quantitative_results.get(period, {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0})
            avg_rate = result.get('avg_hit_rate', 0.0)
            full_rate = result.get('full_hit_rate', 0.0)
            avg_hit_rates.append(avg_rate)
            full_hit_rates.append(full_rate)
        colors = ['#4CAF50', '#FF9800', '#42A5F5', '#9C27B0', '#E91E63', '#F44336']
        chart_colors = colors[:len(periods)]
        bars1 = ax1.bar(periods, avg_hit_rates, color=chart_colors, alpha=0.8)
        ax1.set_title('平均命中率对比', fontsize=12, fontweight='bold', pad=10)
        ax1.set_ylabel('命中率', fontsize=10)
        ax1.set_ylim(0, max(avg_hit_rates) * 1.2 if avg_hit_rates else 1.0)
        ax1.grid(True, alpha=0.3)
        for bar, rate in zip(bars1, avg_hit_rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{rate*100:.2f}%', ha='center', va='bottom', fontweight='bold')
        bars2 = ax2.bar(periods, full_hit_rates, color=chart_colors, alpha=0.8)
        ax2.set_title('全中率对比', fontsize=12, fontweight='bold', pad=10)
        ax2.set_xlabel('优化周期（天）', fontsize=10)
        ax2.set_ylabel('全中率', fontsize=10)
        ax2.set_ylim(0, max(full_hit_rates) * 1.2 if full_hit_rates else 1.0)
        ax2.grid(True, alpha=0.3)
        for bar, rate in zip(bars2, full_hit_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{rate*100:.2f}%', ha='center', va='bottom', fontweight='bold')
        try:
            plt.tight_layout()
        except Exception:
            pass
        canvas = FigureCanvasTkAgg(fig, charts_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def _create_quantitative_table(self, parent_frame):
        print('DEBUG: in _create_quantitative_table, self.quantitative_results =', self.quantitative_results)
        table_title = ttk.Label(parent_frame, text="量化对比结果", font=('Arial', 12, 'bold'))
        table_title.pack(pady=(0, 10))
        table_frame = ttk.Frame(parent_frame)
        table_frame.pack(fill='both', expand=True)
        if not self.quantitative_results:
            no_data_label = ttk.Label(table_frame, text="暂无量化对比结果，请先运行分析", 
                                    font=('Arial', 10), foreground='gray')
            no_data_label.pack(pady=20)
            return
        rates = [(period, self.quantitative_results.get(period, {'avg_hit_rate': 0.0}).get('avg_hit_rate', 0.0)) for period in self.quantitative_periods]
        rates.sort(key=lambda x: x[1], reverse=True)
        period_rank = {period: idx for idx, (period, _) in enumerate(rates)}
        columns = ('周期', '平均命中率', '全中率', '推荐指数')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=6)
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor='center')
        for period in self.quantitative_periods:
            result = self.quantitative_results.get(period, {'avg_hit_rate': 0.0, 'full_hit_rate': 0.0})
            avg_rate = result.get('avg_hit_rate', 0.0)
            full_rate = result.get('full_hit_rate', 0.0)
            rank = period_rank[period]
            if rank == 0:
                recommendation = "⭐⭐⭐ 最优"
            elif rank < 3:
                recommendation = "⭐⭐ 优秀"
            else:
                recommendation = "⭐ 一般"
            tree.insert('', 'end', values=(
                f"{period}天",
                f"{avg_rate*100:.2f}%",
                f"{full_rate*100:.2f}%",
                recommendation
            ))
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        status_text = f"已分析 {len(self.quantitative_results)} 个周期"
        if self.quantitative_results:
            best_period = rates[0][0]
            best_rate = rates[0][1]
            status_text += f" | 最佳周期: {best_period}天 ({best_rate*100:.2f}%)"
        status_label = ttk.Label(parent_frame, text=status_text, font=('Arial', 9), foreground='gray')
        status_label.pack(pady=(5, 0))

    def _create_quantitative_buttons(self, parent_frame):
        """创建量化对比按钮区域"""
        button_frame = ttk.Frame(parent_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        
        # 周期选择器
        period_frame = ttk.LabelFrame(button_frame, text="选择要运行的周期", padding=5)
        period_frame.pack(fill='x', pady=(0, 5))
        
        self.period_vars = {}
        period_checkboxes = {}
        
        for i, period in enumerate(self.quantitative_periods):
            var = tk.BooleanVar(value=True)  # 默认全选
            self.period_vars[period] = var
            
            cb = ttk.Checkbutton(
                period_frame, 
                text=f"{period}天", 
                variable=var,
                style='TCheckbutton'
            )
            cb.grid(row=0, column=i, padx=10, pady=2)
            period_checkboxes[period] = cb
        
        # 按钮区域
        btn_frame = ttk.Frame(button_frame)
        btn_frame.pack(fill='x')
        
        # 运行选中周期按钮
        self.run_selected_btn = ttk.Button(
            btn_frame,
            text="运行选中周期",
            command=self.run_selected_periods,
            style='Accent.TButton'
        )
        self.run_selected_btn.pack(side='left', padx=(0, 5))
        
        # 导出结果按钮
        export_btn = ttk.Button(
            btn_frame,
            text="导出结果",
            command=self.export_quantitative_results
        )
        export_btn.pack(side='left', padx=(0, 5))
        
        # 详细报告按钮
        report_btn = ttk.Button(
            btn_frame,
            text="详细报告",
            command=self.show_quantitative_report
        )
        report_btn.pack(side='left', padx=(0, 5))
        
        # 清空结果按钮
        clear_btn = ttk.Button(
            btn_frame,
            text="清空结果",
            command=self.clear_quantitative_results
        )
        clear_btn.pack(side='right')

    def run_selected_periods(self):
        selected_periods = [period for period, var in self.period_vars.items() if var.get()]
        if not selected_periods:
            messagebox.showwarning("警告", "请至少选择一个周期！")
            return
        self.run_selected_btn.config(state='disabled')
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("量化对比分析")
        progress_dialog.geometry("400x200")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()
        progress_dialog.update_idletasks()
        x = (progress_dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (progress_dialog.winfo_screenheight() // 2) - (200 // 2)
        progress_dialog.geometry(f"400x200+{x}+{y}")
        info_label = ttk.Label(progress_dialog, text=f"正在分析 {len(selected_periods)} 个周期...", font=('Arial', 10))
        info_label.pack(pady=10)
        current_period_label = ttk.Label(progress_dialog, text="", font=('Arial', 9))
        current_period_label.pack(pady=5)
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_dialog, variable=progress_var, maximum=len(selected_periods))
        progress_bar.pack(fill='x', padx=20, pady=10)
        time_label = ttk.Label(progress_dialog, text="已运行: 0秒", font=('Arial', 9))
        time_label.pack(pady=5)
        lock = threading.Lock()
        completed = 0
        start_time = time.time()
        def task(period):
            nonlocal completed
            try:
                result = self._quantitative_backtest(period)
                with lock:
                    self.quantitative_results[period] = {
                        'avg_hit_rate': result[0],
                        'full_hit_rate': result[1]
                    }
                    completed += 1
                    self.save_quantitative_results()  # 每完成一个周期就保存一次
            except Exception as e:
                print(f"【{period}天周期】分析出错: {e}")
                with lock:
                    self.quantitative_results[period] = {
                        'avg_hit_rate': 0.0,
                        'full_hit_rate': 0.0
                    }
                    completed += 1
                    self.save_quantitative_results()
        def update_progress():
            nonlocal completed
            if completed < len(selected_periods):
                elapsed = int(time.time() - start_time)
                remaining = int(elapsed * (len(selected_periods) - completed) / completed) if completed > 0 else 0
                current_period_label.config(text=f"当前进度: {completed}/{len(selected_periods)}")
                time_label.config(text=f"已运行: {elapsed}秒 | 预计剩余: {remaining}秒")
                progress_var.set(completed)
                progress_dialog.after(1000, update_progress)
            else:
                progress_dialog.destroy()
                self.run_selected_btn.config(state='normal')
                self._refresh_quantitative_display()
                messagebox.showinfo("完成", f"量化对比分析完成！\n分析了 {len(selected_periods)} 个周期。")
        def run_comparison():
            with ThreadPoolExecutor(max_workers=min(len(selected_periods), 3)) as executor:
                futures = [executor.submit(task, period) for period in selected_periods]
                for future in futures:
                    future.result()
        threading.Thread(target=run_comparison, daemon=True).start()
        progress_dialog.after(1000, update_progress)

    def clear_quantitative_results(self):
        """清空量化对比结果"""
        if messagebox.askyesno("确认", "确定要清空所有量化对比结果吗？"):
            self.quantitative_results.clear()
            self._refresh_quantitative_display()
            messagebox.showinfo("完成", "量化对比结果已清空！")

    def export_quantitative_results(self):
        """导出量化对比结果"""
        try:
            if not self.quantitative_results:
                messagebox.showwarning("警告", "暂无量化对比结果可导出！")
                return
            # 推荐指数分级
            rates = [(period, result[0] if isinstance(result, tuple) else result.get('avg_hit_rate', 0.0)) for period, result in self.quantitative_results.items()]
            rates.sort(key=lambda x: x[1], reverse=True)
            period_rank = {period: idx for idx, (period, _) in enumerate(rates)}
            # 创建导出内容
            export_content = "量化对比分析结果\n"
            export_content += "=" * 50 + "\n\n"
            export_content += "基于历史数据的优化周期对比实验：\n\n"
            export_content += "优化周期\t平均命中率\t全中率\t推荐指数\n"
            export_content += "-" * 50 + "\n"
            for period in sorted(self.quantitative_results.keys()):
                result = self.quantitative_results[period]
                avg_rate = result[0] if isinstance(result, tuple) else result.get('avg_hit_rate', 0.0)
                full_rate = result[1] if isinstance(result, tuple) else result.get('full_hit_rate', 0.0)
                rank = period_rank[period]
                if rank == 0:
                    recommendation = "⭐⭐⭐ 最优"
                elif rank < 3:
                    recommendation = "⭐⭐ 优秀"
                else:
                    recommendation = "⭐ 一般"
                export_content += f"{period}天\t{avg_rate*100:.2f}%\t{full_rate*100:.2f}%\t{recommendation}\n"
            if self.quantitative_results:
                best_period = rates[0][0]
                best_rate = rates[0][1]
                export_content += f"\n结论：{best_period}天优化周期效果最佳 ({best_rate*100:.2f}%)，建议采用。\n"
            filename = f"量化对比结果_{time.strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(export_content)
            messagebox.showinfo("成功", f"结果已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def run_quantitative_comparison(self):
        """运行量化对比（保留原方法以兼容）"""
        # 现在调用新的选择性运行方法
        self.run_selected_periods()

    def _quantitative_backtest(self, period):
        """对指定周期进行真实回测和参数优化，返回(平均命中率, 全中率)"""
        import numpy as np
        data = self.history_data
        if data is None or len(data) < 101:
            return 0.0, 0.0
        window_size = 100
        version = 8
        hit_rates = []
        full_hit_count = 0
        total_count = 0
        i = window_size
        ran = False
        
        # 主要滚动回测
        while i + period <= len(data):
            ran = True
            train_data = data[i-window_size:i]
            test_data = data[i:i+period]
            
            def eval_params(params):
                model = MFTNModel(params, version)
                model.fit(train_data)
                hits = 0
                for j in range(len(test_data)):
                    pred = model.predict_next()
                    if all(test_data[j][k] in pred[k] for k in range(4)):
                        hits += 1
                return hits / len(test_data)
            
            optimizer = ParameterOptimizer(target_hit_rate=0.8, max_iterations=20, population_size=20, num_threads=1, period=period)
            best_params, _ = optimizer.optimize(eval_params)
            model = MFTNModel(best_params, version)
            model.fit(train_data)
            
            for j in range(len(test_data)):
                pred = model.predict_next()
                pos_hits = [test_data[j][k] in pred[k] for k in range(4)]
                hit_rates.append(sum(pos_hits)/4)
                if all(pos_hits):
                    full_hit_count += 1
                total_count += 1
            i += period
        
        # 如果没有进行滚动回测，但数据足够，则进行单次回测
        if not ran and len(data) > window_size + period:
            # 使用最后的数据进行单次回测
            train_data = data[-window_size-period:-period]
            test_data = data[-period:]
            
            if len(test_data) >= 1:
                def eval_params(params):
                    model = MFTNModel(params, version)
                    model.fit(train_data)
                    hits = 0
                    for j in range(len(test_data)):
                        pred = model.predict_next()
                        if all(test_data[j][k] in pred[k] for k in range(4)):
                            hits += 1
                    return hits / len(test_data)
                
                optimizer = ParameterOptimizer(target_hit_rate=0.8, max_iterations=20, population_size=20, num_threads=1, period=period)
                best_params, _ = optimizer.optimize(eval_params)
                model = MFTNModel(best_params, version)
                model.fit(train_data)
                
                for j in range(len(test_data)):
                    pred = model.predict_next()
                    pos_hits = [test_data[j][k] in pred[k] for k in range(4)]
                    hit_rates.append(sum(pos_hits)/4)
                    if all(pos_hits):
                        full_hit_count += 1
                    total_count += 1
        
        avg_hit_rate = np.mean(hit_rates) if hit_rates else 0.0
        full_hit_rate = full_hit_count / total_count if total_count else 0.0
        return avg_hit_rate, full_hit_rate

    def show_quantitative_report(self):
        """显示详细报告"""
        report_dialog = tk.Toplevel(self.root)
        report_dialog.title("量化对比详细报告")
        report_dialog.geometry("600x500")
        report_dialog.resizable(True, True)
        report_dialog.transient(self.root)
        report_dialog.grab_set()
        text_frame = tk.Frame(report_dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget = tk.Text(text_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                             font=("SimHei", 10))
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)
        # 推荐指数分级
        rates = [(period, result[0] if isinstance(result, tuple) else result.get('avg_hit_rate', 0.0)) for period, result in self.quantitative_results.items()]
        rates.sort(key=lambda x: x[1], reverse=True)
        period_rank = {period: idx for idx, (period, _) in enumerate(rates)}
        # 报告内容
        report_content = """量化对比分析详细报告\n========================\n\n1. 实验背景\n-----------\n本实验基于历史数据，对比了不同优化周期对模型性能的影响。\n通过滚动回测的方式，评估了各优化周期的效果。\n\n2. 实验方法\n-----------\n- 数据来源：历史开奖数据\n- 回测方式：滚动窗口回测\n- 评估指标：平均命中率、全中率\n- 优化算法：遗传算法\n- 测试周期：" + ", ".join([f"{p}天" for p, _ in rates]) + "\n\n3. 实验结果\n-----------\n"""
        for period, _ in rates:
            result = self.quantitative_results[period]
            avg_rate = result[0] if isinstance(result, tuple) else result.get('avg_hit_rate', 0.0)
            full_rate = result[1] if isinstance(result, tuple) else result.get('full_hit_rate', 0.0)
            rank = period_rank[period]
            if rank == 0:
                recommendation = "⭐⭐⭐ 最优"
            elif rank < 3:
                recommendation = "⭐⭐ 优秀"
            else:
                recommendation = "⭐ 一般"
            report_content += f"\n{period}天优化周期：\n- 平均命中率：{avg_rate*100:.2f}%\n- 全中率：{full_rate*100:.2f}%\n- 推荐指数：{recommendation}\n"
        report_content += """\n4. 结论分析\n-----------\n"""
        if rates:
            best_period = rates[0][0]
            best_rate = rates[0][1]
            report_content += f"最佳周期为：{best_period}天，平均命中率最高（{best_rate*100:.2f}%），建议优先采用。\n"
        report_content += """\n5. 建议\n-------\n- 推荐采用最佳周期进行参数优化\n- 定期监控模型性能变化\n- 根据实际效果适当调整优化频率\n- 保持数据更新和模型维护\n\n6. 技术细节\n-----------\n- 优化算法：遗传算法\n- 参数范围：根据经验设定\n- 评估函数：综合命中率和全中率\n- 数据窗口：动态调整\n"""
        text_widget.insert(tk.END, report_content)
        text_widget.config(state=tk.DISABLED)

    def _refresh_quantitative_display(self):
        # 自动刷新量化对比分析区域
        if self.quantitative_analysis_parent_frame is not None:
            for widget in self.quantitative_analysis_parent_frame.winfo_children():
                widget.destroy()
            self._create_quantitative_analysis_frame(self.quantitative_analysis_parent_frame)

    def save_quantitative_results(self):
        """保存量化对比结果到 quantitative_results.json"""
        try:
            with open(self.quantitative_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.quantitative_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存量化对比结果失败: {e}")

    def load_quantitative_results(self):
        """加载量化对比结果到 self.quantitative_results，key 统一为 int"""
        try:
            if hasattr(self, 'quantitative_results_file') and os.path.exists(self.quantitative_results_file):
                with open(self.quantitative_results_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print('DEBUG: loaded json:', data)
                    self.quantitative_results = {int(k): v for k, v in data.items()}
                    print('DEBUG: after int key:', self.quantitative_results)
        except Exception as e:
            print(f"加载量化对比结果失败: {e}")

def main():
    root = tk.Tk()
    root.iconbitmap(default="")  # 避免显示默认图标
    app = LotteryPredictionApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 